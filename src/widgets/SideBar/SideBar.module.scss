.SideBar {
  height: calc(100vh - var(--navbar-height));
  width: var(--sidebar-width);
  background: var(--primary-color);
  position: relative;
  transition: width 0.3s, background-color 0.2s ease;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;
  will-change: width;
  transform: translateZ(0);
}

.Groups {
  margin-bottom: 40px;
}

.GroupMainContainer {
}
.GroupLabel {
  color: var(--primary-color-invert);
}

.collapsed {
  width: var(--sidebar-width-collapsed);
}

.switchers {
  position: absolute;
  bottom: 20px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.lang {
  margin-left: 20px;
}

.Arrow_Button {
  color: var(--primary-color-invert);
  cursor: pointer;
  transform: rotate(180deg);
  margin: auto 10px 10px auto;
  border: none;
  background-color: transparent;
}

.OpenArrow {
  margin: auto auto 10px;
  transform: rotate(0deg);
}

.<PERSON>u {
  height: 100%;
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  &TopBlock {
    flex-grow: 1;
    flex-shrink: 0;
  }
  &BottomBlock {
    flex-grow: 0;
    flex-shrink: 0;
  }
}

.MenuItem {
  color: var(--primary-color-invert);
  height: 34px;
  margin: 8px 0;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  border-radius: 6px;
  cursor: pointer;
  padding: 0 5px;
  transition: hover 0.3s;
  user-select: none;
  background: transparent;
  border: none;

  &:hover {
    background-color: #d3d3d347;
  }
}

.MenuText {
  margin-left: 12px;
  white-space: nowrap;
}

.MenuIcon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.select {
  color: var(--primary-color);
  background-color: var(--primary-color-invert);

  &:hover {
    background-color: var(--primary-color-invert);
    color: var(--primary-color);
  }
}

.openMenu {
}

.IconContainer {
  color: var(--primary-color-invert);
  display: flex;
  padding: 0 5px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  width: 100%;
  border: none;
  background-color: transparent;
}

.TitleProject {
  color: var(--primary-color-invert);
  margin-left: calc(var(--sidebar-width) / 3);
  user-select: none;
}

.VersionProject {
  margin-top: 10px;
  color: var(--primary-color-invert);
  margin-left: calc(var(--sidebar-width) / 3);
  user-select: none;
}
