import { Tooltip } from '@mui/material'
import { useCollapsed } from 'app/providers/CollapsedProvider'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { useEffect, useMemo } from 'react'
import { NavigateFunction } from 'react-router/dist/lib/hooks'
import { useNavigate } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames/classNames'
import { AppVersion } from 'shared/ui/AppVersion'
import { Icon } from 'shared/ui/Icon'
import { type IconNameProps } from 'shared/ui/Icon/Icon.type'
import { useStore } from 'stores/useStore'

import cls from './SideBar.module.scss'

interface IMenuItem {
  key: string
  label: string
  description: string
  access: ROLES[]
  icon: IconNameProps
  order: number
}

const prepareRow = (
  { label, key, icon, description }: IMenuItem,
  history: NavigateFunction,
  collapsed: boolean,
  godMode: boolean,
) => {
  const select = location.pathname === key

  return (
    <div
      className={classNames(
        cls.MenuItem,
        {
          [cls.openMenu]: collapsed,
          [cls.select]: select,
        },
        [],
      )}
      key={`menu-item-${key}`}
      onClick={() => {
        const isEdit = JSON.parse(localStorage.getItem('editMode') as string) ?? false
        if (isEdit) {
          const answer = window.confirm(
            'Изменения сохранены не будут. Вы действительно хотите перейти в другой раздел?',
          )
          if (answer) {
            history(key)
          }
        } else {
          history(key)
        }
      }}
    >
      {icon && (
        <div className={classNames(cls.MenuIcon, { [cls.select]: select, 'god-mode_color': select && !!godMode }, [])}>
          {collapsed ? (
            <Tooltip placement='right' title={description}>
              <div>
                <Icon name={icon} width={32} height={32} />
              </div>
            </Tooltip>
          ) : (
            <Icon name={icon} width={32} height={32} />
          )}
        </div>
      )}
      <div className={classNames(cls.MenuText, { 'god-mode_color': select && !!godMode }, [])}>{label}</div>
    </div>
  )
}

export const SideBar = observer(() => {
  const { authStore, godModeStore } = useStore()
  const { userDetail, appVersion, getAppVersion } = authStore
  const { godMode } = godModeStore
  const { roles } = userDetail
  const history = useNavigate()
  const { collapsed, toggleCollapsed } = useCollapsed()

  useEffect(() => {
    if (appVersion === null) {
      getAppVersion().then()
    }
  }, [appVersion, getAppVersion])

  const onToggle = () => {
    toggleCollapsed()
  }

  const menuList: IMenuItem[] = [
    {
      key: '/rm-wer',
      label: 'РМ ВЭР',
      icon: 'model',
      description: 'РМ ВЭР',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 3,
    },
    {
      key: '/calculation-wer',
      label: 'Расчёты ВЭР',
      icon: 'wave',
      description: 'Расчёты ВЭР',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 6,
    },
    {
      key: '/calcModel',
      label: 'РМ',
      icon: 'calcModel',
      description: 'РМ',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 2,
    },
    {
      key: '/calculations',
      label: 'Расчёты',
      icon: 'bulb',
      description: 'Расчёты',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 4,
    },
    {
      key: '/gaes-calculations',
      label: 'Расчёты ГАЭС',
      icon: 'accumulator',
      description: 'Расчёты ГАЭС',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 5,
    },
    {
      key: '/nsi',
      label: 'НСИ',
      description: 'НСИ',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 1,
      icon: 'nsi',
    },
    {
      key: '/administration',
      label: 'Администрирование',
      description: 'Администрирование',
      access: [ROLES.ADMIN],
      order: -3,
      icon: 'administration',
    },
    {
      key: '/reports',
      label: 'Отчёты',
      description: 'Отчёты',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_NSI, ROLES.TECH_ADMIN_CM, ROLES.TECHNOLOGIST, ROLES.GUEST],
      order: 7,
      icon: 'reports',
    },
    {
      key: '/settings',
      label: 'Настройки',
      description: 'Настройки',
      access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM],
      order: -2,
      icon: 'settings',
    },
    {
      key: '/journals',
      label: 'Журналирование',
      description: 'Журналирование',
      access: [ROLES.ADMIN],
      order: -1,
      icon: 'journals',
    },
  ]

  const menu = useMemo(() => {
    const top: IMenuItem[] = []
    const bottom: IMenuItem[] = []

    if (roles && roles?.length > 0) {
      menuList.forEach((menuItem) => {
        const rolesPrepare = roles.map((item) => item.role)
        const access = menuItem.access
        if (!isViewDevPage && menuItem.key === '/calculation-wer') {
          return
        }
        if (rolesPrepare.some((role) => access.some((accessItem) => accessItem === role))) {
          if (menuItem.order > 0) {
            top.push(menuItem)
          } else {
            bottom.push(menuItem)
          }
        }
      })
    }

    return {
      top: top.sort((a, b) => a.order - b.order),
      bottom: bottom.sort((a, b) => Math.abs(a.order) - Math.abs(b.order)),
    }
  }, [roles])

  return (
    <div
      data-testid='sidebar'
      className={classNames(
        cls.SideBar,
        {
          [cls.collapsed]: collapsed,
          'god-mode_bg-color': !!godMode,
        },
        [],
      )}
    >
      <div className={classNames(cls.Menu, {}, [])}>
        <button
          type='button'
          className={classNames(cls.IconContainer, { [cls.OpenIconContainer]: collapsed }, [])}
          onClick={() => {
            const url = authStore.initialUrl
            history(url)
          }}
        >
          <Icon name='whiteLogo' width={collapsed ? 34 : 100} />
        </button>
        <AppVersion className={classNames(cls.VersionProject, {}, [])} appVersion={appVersion} />
        <h2 className={classNames(cls.TitleProject, {}, [])}>Нептун</h2>
        <div className={cls.MenuTopBlock}>
          {menu.top.map((item) => prepareRow(item, history, collapsed as boolean, godMode))}
        </div>
        <div className={cls.MenuBottomBlock}>
          {menu.bottom.map((item) => prepareRow(item, history, collapsed as boolean, godMode))}
        </div>
      </div>
      <button
        type='button'
        onClick={onToggle}
        className={classNames(cls.Arrow_Button, { [cls.OpenArrow]: collapsed }, [])}
      >
        <Icon name='arrow' width={24} height={24} />
      </button>
    </div>
  )
})
