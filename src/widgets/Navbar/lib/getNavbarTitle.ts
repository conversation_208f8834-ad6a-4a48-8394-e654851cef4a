import { RoutePath } from 'app/routes/routeConfig'

export const getNavbarTitle = (pathname: string): string => {
  const routeTitles: Record<string, string> = {
    [RoutePath.calc_model_wer]: 'РМ ВЭР',
    [RoutePath.calc_model]: 'РМ',
    [RoutePath.calculations]: 'Расчёты',
    [RoutePath.gaes_calculations]: 'Расчёты ГАЭС',
    [RoutePath.nsi]: 'НСИ',
    [RoutePath.administration]: 'Администрирование',
    [RoutePath.reports]: 'Отчёты',
    [RoutePath.settings]: 'Настройки',
    [RoutePath.journals]: 'Журналирование',
    [RoutePath.calculations_wer]: 'Расчёты ВЭР',
  }

  return routeTitles[pathname] || ''
}
