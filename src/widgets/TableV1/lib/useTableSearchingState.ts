import { FilteringStateProps, IntegratedFiltering } from '@devexpress/dx-react-grid'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

export type TableOnSearchingChange = (filtering?: FilteringStateProps['defaultFilters']) => void

type Filters = Exclude<FilteringStateProps['defaultFilters'], undefined>

export const useTableSearchingState = <RowData extends IBaseRowData>(
  columns: IColumn<RowData>[],
  isSearchMode: boolean,
  onFilteringChange?: TableOnSearchingChange,
) => {
  const [filters, setFilters] = useState<Filters>([])

  const columnSearchDisabled = useMemo(
    () => columns.filter((column) => !column.searchingEnabled).map((column) => column.name),
    [],
  )

  const filteringColumnExtensions: IntegratedFiltering.ColumnExtension[] = useMemo(() => {
    const arr: IntegratedFiltering.ColumnExtension[] = []
    for (const column of columns) {
      if (column.onSearch) {
        arr.push({
          columnName: column.name,
          predicate: column.onSearch,
        })
      }
    }

    return arr
  }, [columns])

  const filteringStateColumnExtensions = useMemo(
    () =>
      columnSearchDisabled.map((el) => ({
        columnName: String(el),
        filteringEnabled: false,
      })),
    [columnSearchDisabled],
  )

  const setFiltering = useCallback((updatedFiltering: Filters) => {
    setFilters(updatedFiltering)
    if (onFilteringChange) {
      onFilteringChange(updatedFiltering)
    }
  }, [])

  useEffect(() => {
    if (!isSearchMode) {
      const updatedFiltering = columns
        .filter((column) => column.searchingEnabled)
        .map((column) => ({
          columnName: column.name,
          operation: 'contains',
          value: '',
        }))
      setFiltering(updatedFiltering)
    }
  }, [isSearchMode])

  return {
    filters,
    setFiltering,
    filteringStateColumnExtensions,
    filteringColumnExtensions,
  }
}
