import { createRowCache, RowCache } from '@devexpress/dx-react-grid'
import { useEffect, useReducer, useRef } from 'react'
import { IBaseRowData, TableV1Props } from 'widgets/TableV1'

interface TableState<RowData extends IBaseRowData> {
  skip: number
  requestedSkip: number
  take: number
  rows: RowData[]
  totalCount: number
  loading: boolean
}

type TableAction<RowData extends IBaseRowData> =
  | { type: 'UPDATE_ROWS'; payload: Partial<TableState<RowData>> }
  | { type: 'START_LOADING'; payload: { requestedSkip: number; take: number } }
  | { type: 'REQUEST_ERROR' }
  | { type: 'FETCH_INIT' }

const initialState = <RowData extends IBaseRowData>(pageSize: number): TableState<RowData> => ({
  skip: 0,
  requestedSkip: 0,
  take: pageSize * 2,
  rows: [],
  totalCount: 0,
  loading: false,
})

const buildQueryString = (offset: number | string, limit: number | string): string => `offset=${offset}&limit=${limit}`

function reducer<RowData extends IBaseRowData>(
  state: TableState<RowData>,
  action: TableAction<RowData>,
): TableState<RowData> {
  switch (action.type) {
    case 'UPDATE_ROWS':
      return {
        ...state,
        ...action.payload,
        loading: false,
      }
    case 'START_LOADING':
      return {
        ...state,
        requestedSkip: action.payload.requestedSkip,
        take: action.payload.take,
      }
    case 'REQUEST_ERROR':
      return {
        ...state,
        loading: false,
      }
    case 'FETCH_INIT':
      return {
        ...state,
        loading: true,
      }
    default:
      return state
  }
}

interface UseTableFetchRowsReturn<RowData extends IBaseRowData> {
  skip: number
  totalCount: number
  loading: boolean
  getRemoteRows: (requestedSkip: number, take: number) => void
  pageSize: number
  rows: RowData[]
}

type AsyncFetchParams<RowData extends IBaseRowData> = Exclude<TableV1Props<RowData>['asyncFetchParams'], undefined>

export type VirtualTableCacheControlRef = {
  updateRows: (skip: number, count: number, totalCount: number) => void
  cache: RowCache
}

// https://codesandbox.io/p/sandbox/rg86rd
// Оригинальный компонент из документации, откуда была перенесена данная логика
export const useTableFetchRows = <RowData extends IBaseRowData = IBaseRowData>(
  cacheControlRef: AsyncFetchParams<RowData>['cacheControlRef'] | undefined,
  fetchRows: AsyncFetchParams<RowData>['onFetchRows'] | undefined,
  pageSize: AsyncFetchParams<RowData>['pageSize'] | undefined = 0,
): UseTableFetchRowsReturn<RowData> => {
  const [state, dispatch] = useReducer(reducer, initialState(pageSize))
  const lastQuery = useRef('')

  const updateRows = (skip: number, count: number, totalCount: number): void => {
    dispatch({
      type: 'UPDATE_ROWS',
      payload: {
        skip,
        rows: cacheRef.current.cache.getRows(skip, count) as RowData[],
        totalCount,
      },
    })
  }

  const cacheRef = useRef<VirtualTableCacheControlRef>({
    updateRows,
    cache: createRowCache(pageSize),
  })

  const getRemoteRows = (requestedSkip: number, take: number): void => {
    dispatch({ type: 'START_LOADING', payload: { requestedSkip, take } })
  }

  const loadData = (): void => {
    if (!fetchRows) return

    const { requestedSkip, take, loading, totalCount } = state
    const query = buildQueryString(requestedSkip, take)

    if (query !== lastQuery.current && !loading) {
      const cached = cacheRef.current.cache.getRows(requestedSkip, take)
      if (cached.length === take) {
        updateRows(requestedSkip, take, totalCount)
      } else {
        dispatch({ type: 'FETCH_INIT' })
        fetchRows(requestedSkip, take)
          .then((response) => {
            if (response) {
              cacheRef.current.cache.setRows(requestedSkip, response.rows)
              updateRows(requestedSkip, take, response.totalRows)
            }
          })
          .catch(() => dispatch({ type: 'REQUEST_ERROR' }))
      }
      lastQuery.current = query
    }
  }

  useEffect(() => loadData())

  useEffect(() => {
    if (cacheControlRef) {
      cacheControlRef(cacheRef.current)
    }
  }, [])

  const { skip, totalCount, loading, rows } = state

  return {
    skip,
    totalCount,
    loading,
    getRemoteRows,
    pageSize,
    rows: rows as RowData[],
  }
}
