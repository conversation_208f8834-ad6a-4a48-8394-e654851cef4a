import { CustomTreeDataProps } from '@devexpress/dx-react-grid'
import { IBaseRowData } from 'widgets/TableV1'

export const getChildRows: CustomTreeDataProps['getChildRows'] = (row, rootRows) => (row ? row.children : rootRows)

export const editData = <RowData extends IBaseRowData>(
  data: RowData[],
  changed: Record<string | number, Record<string, unknown>>,
) => {
  return data.map((row) => {
    const children: IBaseRowData[] = row?.children?.length ? editData(row.children, changed) : []

    const changedRow = changed[row.tabId]

    let tempEdits: string[] = []
    if (changedRow && Object.keys(changedRow).length > 0) {
      const changedKey = Object.keys(changedRow)[0]
      const hasChanged = Object.values(changedRow)[0] !== row[changedKey]

      if (hasChanged) {
        if (row?.tempEdits?.length) {
          tempEdits = Array.from(new Set([...row.tempEdits, changedKey]))
        } else {
          tempEdits = [changedKey]
        }
      }
    }

    if (changedRow) {
      return {
        ...row,
        ...changedRow,
        isEdit: tempEdits.length > 0,
        tempEdits,
        children,
        cellsErrors: row.cellsErrors?.filter((item: string) => Object.keys(changed).includes(item)) ?? [],
      }
    }

    return { ...row, children }
  })
}
