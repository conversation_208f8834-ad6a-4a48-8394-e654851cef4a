import { IntegratedSorting, SortingStateProps } from '@devexpress/dx-react-grid'
import { useCallback, useLayoutEffect, useState } from 'react'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

export type TableOnSortingChange = (sorting?: SortingStateProps['defaultSorting']) => void

export const useTableSortingState = <RowData extends IBaseRowData>(
  columns: IColumn<RowData>[],
  onSortingChange?: TableOnSortingChange,
) => {
  // Состояния для контроля сортировки
  const [sortingState, setSortingState] = useState<SortingStateProps['defaultSorting']>([])
  const [defaultSorting, setDefaultSorting] = useState<SortingStateProps['defaultSorting']>([])
  const [columnExtensions, setColumnExtensions] = useState<SortingStateProps['columnExtensions']>([])
  const [integratedColumnExtensions, setIntegratedColumnExtensions] = useState<IntegratedSorting.ColumnExtension[]>([])

  // Установка значений для сортировки после монтирования компонента
  useLayoutEffect(() => {
    const defaultSortingArr: SortingStateProps['defaultSorting'] = []
    const columnExtensionsArr: SortingStateProps['columnExtensions'] = []
    const integratedColumnExtensionsArr: IntegratedSorting.ColumnExtension[] = []

    columns.forEach((column) => {
      if (column.defaultSorting) {
        defaultSortingArr.push({ columnName: column.name, direction: 'asc' })
      }
      if (!column.sortingEnabled) {
        columnExtensionsArr.push({ columnName: column.name, sortingEnabled: false })
      }
      if (column.onSort) {
        integratedColumnExtensionsArr.push({
          columnName: column.name,
          compare: column.onSort,
        })
      }
    })

    setDefaultSorting(defaultSortingArr)
    setColumnExtensions(columnExtensionsArr)
    setIntegratedColumnExtensions(integratedColumnExtensionsArr)

    setSorting(defaultSortingArr)
    if (onSortingChange) {
      onSortingChange(defaultSortingArr)
    }
  }, [])

  const setSorting = useCallback((updatedSorting: SortingStateProps['defaultSorting']) => {
    setSortingState(updatedSorting)
    if (onSortingChange) {
      onSortingChange(updatedSorting)
    }
  }, [])

  return {
    sorting: sortingState,
    setSorting,
    defaultSorting,
    columnExtensions,
    integratedColumnExtensions,
  }
}
