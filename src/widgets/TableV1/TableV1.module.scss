.Table {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loaderContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  z-index: 999;
}

.Head {
  width: 100%;
}

.Body {
  width: 100%;
  height: 100%;
  padding-top: 0.2rem;
}

.Paper {
  box-shadow: none !important;
  height: 100%;
  position: relative;

  th {
    background-color: var(--background-color-primary);
    border-bottom: solid 1px var(--row-color-gray);
    border-right: solid 2px var(--gray-background) !important;
  }

  td {
    position: relative;
    border-right: solid 1px var(--row-color-gray);
  }
}

.DatePicker > div > fieldset {
  padding: 0;
  border-color: transparent;
}

.DatePicker {
  height: 20px !important;
  border-radius: 4px;

  input {
    height: 20px;
    padding: 0 1em !important;
  }

  svg {
    width: 16px !important;
    height: 16px !important;
  }

  button {
    width: 16px !important;
    height: 16px !important;
  }
}

.DatePicker > div > input {
  padding: 0;
}

.EditCell {
  display: flex;
  align-items: center;
  width: 100%;
  > div {
    width: inherit;
  }

  &DatePicker {
    > div {
      max-width: 140px;
    }
  }
}

.CloseIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575 !important;
  width: 20px !important;
  min-width: 20px !important;
  height: 20px !important;
  padding: 0 !important;

  &:hover {
    color: var(--red-color) !important;
  }
}

.TextEdit {
  width: 100%;

  & > div {
    height: 20px !important;
  }
}

.Table th [class*='Mui-disabled'] {
  display: none;
}

.Table tfoot td {
  font-weight: bold;
  color: var(--text-color);
}
.Table th {
  font-weight: bold;
}
