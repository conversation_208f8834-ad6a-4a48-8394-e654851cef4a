import {
  CustomTreeData,
  DataTypeProvider,
  EditingState,
  EditingStateProps,
  FilteringState,
  IntegratedFiltering,
  IntegratedSorting,
  SortingState,
  TableColumnResizingProps,
  TreeDataState,
  VirtualTableState,
} from '@devexpress/dx-react-grid'
import {
  Grid,
  TableBandHeader,
  TableColumnVisibility,
  TableFilterRow,
  TableInlineCellEditing,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui'
import Paper from '@mui/material/Paper'
import {
  CSSProperties,
  type ReactNode,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { useDebouncedFunction } from 'shared/lib/useDebouncedFunction'
import { Loader } from 'shared/ui/Loader'
import { type NumberOption } from 'shared/ui/TextField/model/types.ts'
import { TableOnSearchingChange, useTableSearchingState } from 'widgets/TableV1/lib/useTableSearchingState.ts'
import { SearchNumberEditor } from 'widgets/TableV1/ui/TableHeaderContent/ui/SearchNumberEditor/SearchNumberEditor.tsx'

import {
  editData,
  getChildRows,
  TABLE_DEFAULT_ROW_HEIGHT,
  TABLE_HEADER_HEIGHT,
  TABLE_SEARCH_INPUT_HEIGHT,
  TableOnSortingChange,
  useTableFetchRows,
  useTableSortingState,
  VirtualTableCacheControlRef,
} from './lib'
import cls from './TableV1.module.scss'
import {
  EditCell,
  TableBandCell,
  TableBandCellProps,
  TableCell,
  TableFixedColumns,
  TableHeaderContent,
  TableHeaderContentProps,
  TableResizing,
  TableRow,
  TableSelectionColumn,
  TableSelectionColumnProps,
  TableTreeColumn,
  TTableColumnFiltering,
} from './ui'

const MIN_WIDTH = 300

interface IEditingCell {
  rowId: string | number
  columnName: string
}

export interface IBaseRowData {
  tabId: number
  children?: IBaseRowData[]
  tempEdits?: string[]
  cellsErrors?: string[]
  type?: string
  isEdit?: boolean
  disabledChecked?: boolean
  disabledEditColumns?: string[]
  stylesCell?: Record<string, CSSProperties>
  action?: string
  [key: string]: any
}

export interface IColumn<RowData extends IBaseRowData, Name extends keyof RowData = keyof RowData> {
  name: string
  width: number | string
  title?: string
  fixed?: 'leftColumns' | 'rightColumns'
  selectDataFromRow?: string

  // Включение сортировки по колонке
  sortingEnabled?: boolean
  // Значение по умолчанию для сортировки
  defaultSorting?: boolean
  // Кастомный обработчик для установки произвольной функции сравнения
  onSort?: IntegratedSorting.ColumnExtension['compare']

  // Включение поиска по колонке
  searchingEnabled?: boolean
  // Кастомный обработчик для поиска по колонке
  onSearch?: IntegratedFiltering.ColumnExtension['predicate']

  filtering?: TTableColumnFiltering

  editType?: 'text' | 'date' | 'groups' | 'number' | 'positiveNumber' | 'switch' | 'select' | 'time'
  editableMaxLength?: number

  editingEnabled?: ((row: RowData) => boolean) | boolean
  tempEdits?: string[]

  // Опции для работы с редактируемым значением типа number
  minNumber?: number
  maxNumber?: number
  toFixed?: number
  positiveNumber?: boolean
  isInteger?: boolean
  numberOption?: NumberOption

  // Блокировка дат в прошлом считая от текущих
  disablePast?: boolean

  canClearCell?: boolean
  isHeadLabelCenter?: boolean
  isHeadLabelRight?: boolean
  column?: { title: string }
  onAfterChange?: (value: any, row: RowData) => void
  headRender?: (title?: string) => ReactNode
  render?: (value: any, row: RowData) => ReactNode
  tooltip?: (value: RowData[Name], row: RowData) => ReactNode
  getCellClassName?: (value: RowData[Name]) => string
  getCellRowSpan?: (value: RowData) => number
  // Значение, которое отображается при копировании из контекстного меню, либо когда не описан callback - render
  getCellRenderValue?: (value: any, row: RowData) => string | undefined
}

export interface TableV1Props<RowData extends IBaseRowData = IBaseRowData> {
  rows?: RowData[]
  setRows?: (rows: RowData[]) => void
  columns: IColumn<RowData>[]
  className?: string
  headerComponents?: ReactNode
  maxVisibleRows?: number
  rowHeight: number | 'auto'
  editMode?: boolean
  loading?: boolean
  enabledSelectMode?: boolean
  childKey?: string
  selection?: TableSelectionColumnProps<RowData>['selection']
  showSelectAll?: TableSelectionColumnProps<RowData>['showSelectAll']
  setSelection?: (ids: string[]) => void
  expandedRowIds?: (string | number)[]
  setExpandedRowIds?: (ids: (string | number)[]) => void
  editingCells?: IEditingCell[]
  setEditingCells?: (cells: IEditingCell[]) => void
  // заблокировать поиск для выбранных колонок
  columnBands?: TableBandCellProps['columnBands']
  searchNumberEditorColumns?: string[]
  initSearchMode?: boolean
  headStyle?: CSSProperties
  hiddenColumnNames?: string[]
  setHiddenColumnNames?: (names: string[]) => void
  onColumnWidthsChange?: TableColumnResizingProps['onColumnWidthsChange']
  asyncFetchParams?:
    | {
        onFetchRows: (skip: number, take: number) => Promise<{ rows: RowData[]; totalRows: number } | undefined>
        pageSize: number
        cacheControlRef: (ref: VirtualTableCacheControlRef | null) => void
      }
    | undefined
  getRowId?: (row: RowData) => string | number
  onTopRowChange?: (changes?: [number, number]) => void
  onSortingChange?: TableOnSortingChange
  onSearchingChange?: TableOnSearchingChange
  onFilteringChange?: TableHeaderContentProps<RowData>['onFilteringChange']
}

export const TableV1 = <RowData extends IBaseRowData>(props: TableV1Props<RowData>) => {
  const {
    rows: externalRows,
    setRows,
    columns,
    className,
    headerComponents = null,
    enabledSelectMode = false,
    selection,
    setSelection,
    childKey = '',
    editMode = false,
    expandedRowIds = null,
    setExpandedRowIds = null,
    showSelectAll = true,
    columnBands = [],
    searchNumberEditorColumns = [],
    initSearchMode = false,
    editingCells = undefined,
    setEditingCells = undefined,
    headStyle = {},
    hiddenColumnNames = null,
    setHiddenColumnNames = () => {},
    maxVisibleRows,
    rowHeight,
    loading = false,
    onColumnWidthsChange,
    asyncFetchParams,
    getRowId,
    onTopRowChange,
    onSortingChange,
    onSearchingChange,
    onFilteringChange,
  } = props
  const [localLoading, setLocalLoading] = useState<boolean>(true)
  const [width, setWidth] = useState<null | string>(null)
  const [height, setHeight] = useState<null | string>(null)
  const [expandedRowIdsLocal, setExpandedRowIdsLocal] = useState<(string | number)[]>([])
  const [isSearchMode, setIsSearchMode] = useState<boolean>(initSearchMode)

  // используется, если не были переданы: editingCells, setEditingCells
  const [editingCellsLocal, setEditingCellsLocal] = useState<IEditingCell[]>([])

  const rootRef = useRef<HTMLDivElement>(null)

  const debouncedSetWidth = useDebouncedFunction(setWidth, 50)
  const debouncedSetHeight = useDebouncedFunction(setHeight, 50)

  const { sorting, setSorting, defaultSorting, columnExtensions, integratedColumnExtensions } = useTableSortingState(
    columns,
    onSortingChange,
  )

  const { filters, setFiltering, filteringColumnExtensions, filteringStateColumnExtensions } = useTableSearchingState(
    columns,
    isSearchMode,
    onSearchingChange,
  )

  const {
    skip,
    getRemoteRows,
    loading: virtualLoading,
    totalCount,
    pageSize,
    rows: virtualRows,
  } = useTableFetchRows<RowData>(
    asyncFetchParams?.cacheControlRef,
    asyncFetchParams?.onFetchRows,
    asyncFetchParams?.pageSize,
  )

  const rows = useMemo(() => {
    if (virtualRows.length > 0) {
      return virtualRows
    }
    if (externalRows && externalRows?.length > 0) {
      return externalRows
    }

    return []
  }, [externalRows, virtualRows])

  const virtualTableColumnExtensions = useMemo(() => {
    return (
      columns.map((column) => ({
        columnName: column.name,
        width: column.width,
      })) || []
    )
  }, [columns])

  useLayoutEffect(() => {
    let maxVirtualTableHeight = 0
    if (rows.length === 0) {
      maxVirtualTableHeight = TABLE_DEFAULT_ROW_HEIGHT + TABLE_HEADER_HEIGHT
      if (isSearchMode) {
        // прибавляем высоту инпутов, которые появляются при поиске по колонке
        maxVirtualTableHeight += TABLE_SEARCH_INPUT_HEIGHT
      }
    } else if (maxVisibleRows && typeof rowHeight === 'number') {
      maxVirtualTableHeight = maxVisibleRows * rowHeight + TABLE_HEADER_HEIGHT
    }

    const observer = new ResizeObserver((entries) => {
      const target = entries[0].target
      const { width, height } = entries[0].contentRect
      const tableTarget = target.getElementsByClassName('TableContainer-root')[0]

      if (maxVisibleRows && rowHeight === 'auto') {
        const tbody = tableTarget.getElementsByTagName('tbody')[0]
        const trows = Array.from(tbody.getElementsByTagName('tr'))
        const rowHeights = trows.map((tr) => tr.clientHeight).slice(0, maxVisibleRows)
        maxVirtualTableHeight =
          TABLE_HEADER_HEIGHT + rowHeights.reduce((accumulator, currentValue) => accumulator + currentValue, 0)
      }

      if (maxVirtualTableHeight === 0) {
        maxVirtualTableHeight = height
      }

      if (tableTarget.scrollHeight > maxVirtualTableHeight) {
        // Если таблица ограничена по высоте и умещается по ширине, то для скрытия горизонтального
        // скролла необходимо увеличить ширину на 9px когда появляется вертикальный скролл
        debouncedSetWidth(String(width + 9))
        debouncedSetHeight(`${maxVirtualTableHeight}px`)
      } else {
        debouncedSetWidth(String(width))
        debouncedSetHeight('auto')
      }
    })

    if (rootRef.current) {
      observer.observe(rootRef.current)
    }

    return () => {
      if (rootRef.current) {
        observer.disconnect()
      }
    }
  }, [rows.length, maxVisibleRows, rowHeight, isSearchMode])

  useEffect(() => {
    // Отображаем лоудер на 100 млс больше, чтобы таблицы успела отрисоваться и как следствие
    // исчез эффект промаргивания таблицы
    if (!loading) {
      setTimeout(() => {
        setLocalLoading(loading)
      }, 100)
    } else {
      setLocalLoading(loading)
    }
  }, [loading])

  const commitChanges: EditingStateProps['onCommitChanges'] = useCallback(
    ({ changed }) => {
      if (changed && setRows) {
        const changedRows = editData(rows, changed)
        setRows(changedRows)
      }
    },
    [rows, setRows],
  )

  const tableRef = useRef<any>(null)
  const handleTopRowChange = () => {
    if (onTopRowChange) {
      onTopRowChange(tableRef.current?.state.viewport.rows)
    }
  }

  return (
    <div id='table-container' ref={rootRef} className={classNames(cls.Table, {}, [className || ''])}>
      {headerComponents && (
        <div style={headStyle} className={cls.Head}>
          {headerComponents}
        </div>
      )}
      <div className={cls.Body}>
        <Paper
          style={{
            width: width ? Number(width) : MIN_WIDTH,
          }}
          className={cls.Paper}
        >
          {/* Grid — это корневой компонент-контейнер, предназначенный для обработки */}
          {/* и отображения данных, указанных через свойство rows. Свойство columns можно */}
          {/* использовать для настройки столбцов  */}
          <Grid rows={rows} columns={columns} getRowId={getRowId}>
            {/* КАСТОМНЫЙ EDITOR ДЛЯ СТРОКИ ПОИСКА */}
            <DataTypeProvider
              for={searchNumberEditorColumns}
              availableFilterOperations={['equal']}
              editorComponent={SearchNumberEditor}
            />

            {/* ВЫБОР СТРОК С ПОМОЩЬЮ ЧЕКБОКСОВ */}
            {/* Кастомный компонент для отображения первой колонки с чекбоксами */}
            {setSelection && enabledSelectMode && (
              <TableSelectionColumn
                rows={rows}
                selection={selection}
                showSelectAll={showSelectAll}
                setSelection={setSelection}
              />
            )}

            {/* УПРАВЛЕНИЕ СОРТИРОВКОЙ В СТОЛБЦЕ */}
            {/* Плагин, управляющий состоянием сортировки. Он управляет списком столбцов, участвующих в сортировке. */}
            <SortingState defaultSorting={defaultSorting} columnExtensions={columnExtensions} sorting={sorting} />
            {/*/!* Плагин, выполняющий встроенную сортировку данных. *!/*/}
            {!onSortingChange && <IntegratedSorting columnExtensions={integratedColumnExtensions} />}

            {/* УПРАВЛЕНИЕ ВИРТУАЛИЗАЦИЕЙ ТАБЛИЦЫ */}
            {asyncFetchParams && (
              <VirtualTableState
                infiniteScrolling
                loading={virtualLoading || loading}
                totalRowCount={totalCount}
                pageSize={pageSize}
                skip={skip}
                getRows={getRemoteRows}
              />
            )}
            {/* Плагин, который отображает прокручиваемую таблицу вместо статической. */}
            <VirtualTable
              ref={tableRef}
              height={height ?? 'auto'}
              estimatedRowHeight={typeof rowHeight === 'number' ? rowHeight : TABLE_DEFAULT_ROW_HEIGHT}
              cellComponent={(props) => <TableCell editMode={editMode} {...props} />}
              rowComponent={TableRow}
              messages={{ noData: 'Нет данных' }}
              columnExtensions={virtualTableColumnExtensions}
              onTopRowChange={handleTopRowChange}
            />

            {/* УПРАВЛЕНИЕ РАЗМЕРОМ КОЛОНОК */}
            {/* Плагин, управляющий шириной столбцов таблицы. */}
            <TableResizing columns={columns} onColumnWidthsChange={onColumnWidthsChange} />

            {/* УПРАВЛЕНИЕ СКРЫТИЕМ КОЛОНОК */}
            {/* Плагин, управляющий видимостью столбцов сетки. */}
            {hiddenColumnNames && (
              <TableColumnVisibility
                hiddenColumnNames={hiddenColumnNames}
                onHiddenColumnNamesChange={setHiddenColumnNames}
              />
            )}

            {/* УПРАВЛЕНИЕ ЗАГОЛОВКАМИ КОЛОНОК */}
            {/* Плагин, который управляет отображением заголовков столбца */}
            <TableHeaderContent
              childKey={childKey}
              rows={rows}
              sorting={sorting}
              expandedRowIds={expandedRowIds}
              expandedRowIdsLocal={expandedRowIdsLocal}
              filters={filters}
              setIsSearchMode={setIsSearchMode}
              onSortingChange={setSorting}
              setExpandedRowIds={setExpandedRowIds}
              setExpandedRowIdsLocal={setExpandedRowIdsLocal}
              onFilteringChange={onFilteringChange}
            />

            {/* УПРАВЛЕНИЕ ДЕРЕВОМ */}
            {/* Плагин, управляющий развернутым состоянием строк дерева. */}
            <TreeDataState
              expandedRowIds={expandedRowIds || expandedRowIdsLocal}
              onExpandedRowIdsChange={(newValue) => {
                if (setExpandedRowIds) {
                  setExpandedRowIds(newValue)
                } else {
                  setExpandedRowIdsLocal(newValue)
                }
              }}
            />
            {/* Плагин, который преобразует данные дерева пользовательского формата */}
            {/* в поддерживаемый формат и выполняет локальное развертывание/свертывание строк. */}
            <CustomTreeData getChildRows={getChildRows} />
            {/* Плагин, который отображает столбец таблицы с кнопкой скрытия раскрытия строк. */}
            <TableTreeColumn childKey={childKey} rows={rows} />

            {/* УПРАВЛЕНИЕ ВЛОЖЕННОЙ ШАПКОЙ */}
            {columnBands.length > 0 && (
              <TableBandHeader
                columnBands={columnBands}
                cellComponent={(props) => <TableBandCell {...props} columnBands={columnBands} />}
              />
            )}

            {/* УПРАВЛЕНИЕ ФИЛЬТРАЦИЕЙ */}
            {/* Плагин, управляющий состоянием фильтрации. */}
            <FilteringState
              defaultFilters={[]}
              filters={filters}
              onFiltersChange={setFiltering}
              columnExtensions={filteringStateColumnExtensions}
            />
            <IntegratedFiltering columnExtensions={filteringColumnExtensions} />
            {/* Плагин, отображающий строку фильтра. */}
            {isSearchMode && (
              <TableFilterRow
                messages={{
                  filterPlaceholder: 'Поиск',
                }}
              />
            )}

            {/* УПРАВЛЕНИЕ ФИКСАЦИЕЙ КОЛОНОК СПРАВА ИЛИ СЛЕВА */}
            <TableFixedColumns columns={columns} enabledSelectMode={enabledSelectMode} />

            {/* УПРАВЛЕНИЕ РЕДАКТИРОВАНИЕМ */}
            {/* Плагин, который управляет состоянием редактирования строк сетки. */}
            {/* Он упорядочивает строки сетки по разным спискам в зависимости от состояния строки.*/}
            <EditingState
              onCommitChanges={commitChanges}
              editingCells={editingCells || editingCellsLocal}
              onEditingCellsChange={setEditingCells || setEditingCellsLocal}
            />
            {/* Плагин, визуализирующий редактируемую ячейку. */}
            {setRows && (
              <TableInlineCellEditing
                startEditAction='click'
                selectTextOnEditStart
                cellComponent={(props) => <EditCell {...props} editMode={editMode} rows={rows} setRows={setRows} />}
              />
            )}
          </Grid>
        </Paper>
      </div>
      {localLoading && (
        <div className={cls.loaderContainer}>
          <Loader />
        </div>
      )}
    </div>
  )
}
