.cell {
  width: 100%;
  height: 100%;
  min-width: 100%;
  min-height: 24px;
  font-size: 14px;
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;

  &Switch {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.editable {
  border-radius: 6px;
  cursor: pointer;
  border: solid 1px transparent;

  &:hover {
    border: solid 1px var(--primary-color);
    cursor: pointer !important;
  }
}

.error {
  border: 1px solid var(--red-color);
}
