import { Filter } from '@devexpress/dx-react-grid'
import { TableHeaderRow, TableHeaderRowProps } from '@devexpress/dx-react-grid-material-ui'
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react'
import { prepareFlatData } from 'shared/lib/prepareData'
import { IBaseRowData } from 'widgets/TableV1'
import { TableHeaderCell } from 'widgets/TableV1/ui/TableHeaderContent/ui/TableHeaderCell/TableHeaderCell.tsx'

import { FilterControlProps, SortControlProps } from './ui'

export interface TableHeaderContentProps<RowData extends IBaseRowData> {
  childKey: string
  rows: RowData[]
  expandedRowIds: (string | number)[] | null
  expandedRowIdsLocal: (string | number)[]
  filters: Filter[]
  setIsSearchMode: Dispatch<SetStateAction<boolean>>
  sorting: SortControlProps<RowData>['sorting']
  onSortingChange: SortControlProps<RowData>['onSortingChange']
  setExpandedRowIds: ((ids: (string | number)[]) => void) | null
  setExpandedRowIdsLocal: (ids: (string | number)[]) => void
  onFilteringChange?: FilterControlProps<RowData>['onFilter']
}

export const TableHeaderContent = <RowData extends IBaseRowData>(props: TableHeaderContentProps<RowData>) => {
  const {
    childKey,
    rows,
    expandedRowIds,
    expandedRowIdsLocal,
    setIsSearchMode,
    sorting,
    onSortingChange,
    filters,
    setExpandedRowIds,
    setExpandedRowIdsLocal,
    onFilteringChange,
  } = props
  const [expandedAll, setExpandedAll] = useState(true)
  const isTree = rows.some((el) => el?.children)
  const flatRows = useMemo(() => (isTree ? prepareFlatData(rows) : []), [rows])

  const onOpenAll = useCallback(() => {
    const tempRows = flatRows?.filter((item) => item).map((el) => el.tabId)

    if (expandedRowIds) {
      setExpandedRowIds?.(tempRows)
    } else {
      setExpandedRowIdsLocal(tempRows)
    }
  }, [flatRows, expandedRowIds, setExpandedRowIds])

  const onCloseAll = useCallback(() => {
    if (expandedRowIds) {
      setExpandedRowIds?.([])
    } else {
      setExpandedRowIdsLocal([])
    }
  }, [expandedRowIds, setExpandedRowIds])

  useEffect(() => {
    if (filters.length > 0) {
      const isWord = filters.some((el) => el.value.trim()?.length > 0)
      if (isWord) {
        if (!expandedAll) {
          onOpenAll()
        }
      } else {
        onCloseAll()
      }
    }
  }, [filters, expandedAll, onOpenAll, onCloseAll])

  useEffect(() => {
    if (expandedRowIds) {
      if (expandedRowIds?.length === 0) {
        setExpandedAll(false)
      } else {
        setExpandedAll(flatRows?.filter((item) => item).length === expandedRowIds.length)
      }
    } else {
      if (expandedRowIdsLocal.length === 0) {
        setExpandedAll(false)
      } else {
        setExpandedAll(flatRows?.filter((item) => item).length === expandedRowIdsLocal?.length)
      }
    }
  }, [expandedRowIdsLocal, expandedRowIds, flatRows])

  const renderContentComponent = useCallback<Exclude<TableHeaderRowProps['contentComponent'], undefined>>(
    (props) => (
      <TableHeaderCell
        {...props}
        childKey={childKey}
        setIsSearchMode={setIsSearchMode}
        sorting={sorting}
        onSortingChange={onSortingChange}
        onFilteringChange={onFilteringChange}
        expandedAll={expandedAll}
        onOpenAll={onOpenAll}
        onCloseAll={onCloseAll}
      />
    ),
    [childKey, setIsSearchMode, sorting, onSortingChange, onFilteringChange, expandedAll],
  )

  return <TableHeaderRow contentComponent={renderContentComponent} />
}
