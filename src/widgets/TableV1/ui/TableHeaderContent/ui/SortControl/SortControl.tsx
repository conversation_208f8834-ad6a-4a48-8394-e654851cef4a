import { SortingStateProps } from '@devexpress/dx-react-grid'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp'
import { classNames } from 'shared/lib/classNames'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

import cls from './SortControl.module.scss'

export interface SortControlProps<RowData extends IBaseRowData> {
  column: Omit<IColumn<RowData>, 'width'>
  onSortingChange: (sorting: SortingStateProps['defaultSorting']) => void
  sorting: SortingStateProps['defaultSorting']
}

export const SortControl = <RowData extends IBaseRowData>(props: SortControlProps<RowData>) => {
  const { column, onSortingChange, sorting } = props

  const handleSortingChange = () => {
    const res: SortingStateProps['defaultSorting'] = []
    if (sorting && sorting.length > 0 && sorting[0].columnName === column.name) {
      if (sorting[0].direction === 'asc') {
        res.push({
          columnName: column.name,
          direction: 'desc',
        })
      }
    } else {
      res.push({
        columnName: column.name,
        direction: 'asc',
      })
    }
    onSortingChange(res)
  }

  if (column?.sortingEnabled) {
    let isUpSort = false
    let isDownSort = false

    if (sorting && sorting.length > 0) {
      isUpSort = sorting[0].columnName === column.name && sorting[0].direction === 'asc'
      isDownSort = sorting[0].columnName === column.name && sorting[0].direction === 'desc'
    }

    return (
      <button type='button' onClick={handleSortingChange} className={cls.arrowContainer}>
        <div className={classNames(cls.arrow, { [cls.selectSort]: isUpSort })}>
          <ArrowDropUpIcon />
        </div>
        <div className={classNames(cls.arrow, { [cls.selectSort]: isDownSort })}>
          <ArrowDropDownIcon />
        </div>
      </button>
    )
  }

  return null
}
