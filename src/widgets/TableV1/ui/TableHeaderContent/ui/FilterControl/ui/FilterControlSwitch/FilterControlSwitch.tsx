import { ChangeEvent, FC } from 'react'
import { Switch } from 'shared/ui/Switch'

export type TFilterControlSwitchFiltering = {
  type: 'switch'
  value: boolean
  onFilter?: (value: boolean) => void
}

export interface FilterControlSwitchProps {
  columnName: string
  filtering: TFilterControlSwitchFiltering
  onFilter?: (type: TFilterControlSwitchFiltering['type'], columnName: string, value: boolean) => void
}

export const FilterControlSwitch: FC<FilterControlSwitchProps> = (props) => {
  const { columnName, filtering, onFilter } = props

  const handleChange = (_: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    if (filtering?.onFilter) {
      filtering?.onFilter(checked)
    }
    if (onFilter) {
      onFilter('switch', columnName, checked)
    }
  }

  return <Switch checked={filtering.value} onChange={handleChange} />
}
