import { Checkbox, FormControlLabel, MenuItem } from '@mui/material'
import { ChangeEvent, FC } from 'react'
import { classNames } from 'shared/lib/classNames'

import cls from '../../FilterControl.module.scss'

interface IFilterControlSelectItem {
  value: string
  label: string
  checked: boolean
}

export type TFilterControlMultiSelectFiltering = {
  type: 'multiselect'
  items: IFilterControlSelectItem[]
  onFilter?: (values: string[]) => void
}

export interface FilterControlMultiSelectProps {
  columnName: string
  filtering: TFilterControlMultiSelectFiltering
  onFilter?: (type: TFilterControlMultiSelectFiltering['type'], columnName: string, values: string[]) => void
}

export const FilterControlMultiSelect: FC<FilterControlMultiSelectProps> = (props) => {
  const { columnName, filtering, onFilter } = props

  const handleChangeFilter = (event: ChangeEvent<HTMLInputElement>) => {
    const newItems = filtering.items ? [...filtering.items] : []
    if (newItems.length > 0) {
      const index = newItems.findIndex((item) => item.value === event.target.name)
      newItems.splice(index, 1, {
        ...newItems[index],
        checked: event.target.checked,
      })
      if (filtering.onFilter) {
        filtering?.onFilter(newItems.filter((item) => item.checked).map((item) => item.value))
      }
      if (onFilter) {
        onFilter(
          'multiselect',
          columnName,
          newItems.filter((item) => item.checked).map((item) => item.value),
        )
      }
    }
  }

  return (
    <>
      {filtering.items.map((item) => (
        <MenuItem key={item.value} className={classNames(cls.filterItem, {}, [])}>
          <FormControlLabel
            control={<Checkbox checked={item.checked} name={item.value} onChange={handleChangeFilter} />}
            label={item.label}
          />
        </MenuItem>
      ))}
    </>
  )
}
