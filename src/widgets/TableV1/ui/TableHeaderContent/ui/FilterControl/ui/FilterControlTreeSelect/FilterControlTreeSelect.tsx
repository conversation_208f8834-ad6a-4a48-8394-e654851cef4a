import './FilterControlTreeSelect.scss'

import { SimpleTreeView, SimpleTreeViewProps, TreeItem } from '@mui/x-tree-view'
import { FC, ReactNode } from 'react'

interface IFilterControlTreeSelectItem {
  value: string
  label: string
  checked: boolean
  children?: IFilterControlTreeSelectItem[]
}

export type TFilterControlTreeSelectFiltering = {
  type: 'treeselect'
  items: IFilterControlTreeSelectItem[]
  onFilter?: (values: string[]) => void
}

export interface FilterControlTreeSelectProps {
  columnName: string
  filtering: TFilterControlTreeSelectFiltering
  onFilter?: (type: TFilterControlTreeSelectFiltering['type'], columnName: string, values: string[]) => void
}

const getAllCheckedValues = (items: IFilterControlTreeSelectItem[]): string[] => {
  const values: string[] = []
  items.forEach((item) => {
    if (item.checked) {
      values.push(item.value)
    }
    if (item.children) {
      values.push(...getAllCheckedValues(item.children))
    }
  })

  return values
}

const getExpandedIdx = (items: IFilterControlTreeSelectItem[]): string[] => {
  const values: string[] = []
  items.forEach((item) => {
    if (item.children && item.children.length > 0) {
      values.push(item.value)
    }
    if (item.children) {
      values.push(...getExpandedIdx(item.children))
    }
  })

  return values
}

const renderTreeItems = (items: IFilterControlTreeSelectItem[]): ReactNode => {
  return items.map((item) => (
    <TreeItem key={item.value} className='treeItem' itemId={item.value} label={item.label}>
      {item.children && renderTreeItems(item.children)}
    </TreeItem>
  ))
}

export const FilterControlTreeSelect: FC<FilterControlTreeSelectProps> = (props) => {
  const { columnName, filtering, onFilter } = props

  const handleSelectedItemsChange: SimpleTreeViewProps<true>['onSelectedItemsChange'] = (_, itemIds) => {
    const selectedValues = itemIds || []

    if (filtering.onFilter) {
      filtering.onFilter(selectedValues)
    }

    if (onFilter) {
      onFilter('treeselect', columnName, selectedValues)
    }
  }

  const selectedItems = getAllCheckedValues(filtering.items || [])

  const expandedItems = getExpandedIdx(filtering.items || [])

  return (
    <SimpleTreeView
      checkboxSelection
      multiSelect
      defaultExpandedItems={expandedItems}
      selectedItems={selectedItems}
      onSelectedItemsChange={handleSelectedItemsChange}
      className='treeView'
    >
      {renderTreeItems(filtering.items || [])}
    </SimpleTreeView>
  )
}
