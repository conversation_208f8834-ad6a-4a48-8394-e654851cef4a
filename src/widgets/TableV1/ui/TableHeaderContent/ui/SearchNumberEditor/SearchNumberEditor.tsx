import { DataTypeProvider } from '@devexpress/dx-react-grid'
import { Input, InputProps } from '@mui/material'
import { FC } from 'react'

import cls from './SearchNumberEditor.module.scss'

export const SearchNumberEditor: FC<DataTypeProvider.ValueEditorProps> = ({ value, onValueChange }) => {
  const handleChange: InputProps['onChange'] = (event) => {
    const { value: targetValue } = event.target
    onValueChange(targetValue)
  }

  return (
    <Input
      fullWidth
      type='number'
      className={cls.numericInput}
      value={value === undefined ? '' : value}
      inputProps={{ placeholder: 'Поиск' }}
      onChange={handleChange}
    />
  )
}
