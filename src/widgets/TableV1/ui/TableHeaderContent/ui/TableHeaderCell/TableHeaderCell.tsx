import { TableHeaderRow } from '@devexpress/dx-react-grid-material-ui'
import SearchIcon from '@mui/icons-material/Search'
import { Dispatch, SetStateAction } from 'react'
import { classNames } from 'shared/lib/classNames'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

import { FilterControl, FilterControlProps } from '../FilterControl'
import { SortControl, SortControlProps } from '../SortControl'
import { TreeExpandControl } from '../TreeExpandControl'
import cls from './TableHeaderCell.module.scss'

export const TableHeaderCell = <RowData extends IBaseRowData>({
  column,
  childKey,
  setIsSearchMode,
  sorting,
  onSortingChange,
  onFilteringChange,
  expandedAll,
  onOpenAll,
  onCloseAll,
}: {
  column: Omit<IColumn<RowData>, 'width'>
  childKey: string
  setIsSearchMode: Dispatch<SetStateAction<boolean>>
  sorting: SortControlProps<RowData>['sorting']
  onSortingChange: SortControlProps<RowData>['onSortingChange']
  onFilteringChange?: FilterControlProps<RowData>['onFilter']
  expandedAll: boolean
  onOpenAll: () => void
  onCloseAll: () => void
}) => {
  const isChildCell = childKey === column.name

  if (column.headRender) {
    return <div className={cls.customTableTdCell}>{column.headRender(column?.title)}</div>
  }

  return (
    <TableHeaderRow.Content column={column} align='center'>
      <div className={classNames(cls.tableHeaderRowContent)}>
        {isChildCell && <TreeExpandControl expandedAll={expandedAll} onClose={onCloseAll} onOpen={onOpenAll} />}
        <div className={cls.tableHeaderTdContent}>
          {column.title !== '' && (
            <div
              className={classNames(cls.tableHeaderTdContentTitle, {
                [cls.tableHeaderTdContentTitleCenter]: column.isHeadLabelCenter,
                [cls.tableHeaderTdContentTitleRight]: column.isHeadLabelRight,
              })}
            >
              {column.title}
            </div>
          )}
          <div
            className={classNames(cls.headerIcons, {
              [cls.headerIconsHidden]: !column.searchingEnabled && !column?.sortingEnabled && !column.filtering,
            })}
          >
            {column.searchingEnabled && (
              <button type='button' onClick={() => setIsSearchMode((prev) => !prev)} className={cls.searchContainer}>
                <SearchIcon />
              </button>
            )}
            <SortControl column={column} onSortingChange={onSortingChange} sorting={sorting} />
            <FilterControl column={column} onFilter={onFilteringChange} />
          </div>
        </div>
      </div>
    </TableHeaderRow.Content>
  )
}
