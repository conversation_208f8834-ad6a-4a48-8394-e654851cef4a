.customTableTdCell {
  padding: 0 !important;
  width: 100%;
}

.tableHeaderRowContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}


.tableHeaderTdContent {
  width: 100%;
  display: flex;

  &Title {
    flex: 1 0 auto;

    &Center {
      text-align: center;
    }

    &Right {
      text-align: right;
    }
  }
}

.headerIcons {
  flex: 0 1 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &Hidden {
    display: none;
  }
}

.searchContainer {
  height: 20px;
  width: 20px;
  min-width: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  outline: none;
  background-color: transparent;
  cursor: pointer;
}

.arrowContainer {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  width: 16px;
  height: 20px;
  outline: none;
  border: none;
  background-color: transparent;
}
