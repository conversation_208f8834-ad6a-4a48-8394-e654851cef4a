import { TableTreeColumn as TableTreeColumnBase } from '@devexpress/dx-react-grid'
import { TableTreeColumn } from '@devexpress/dx-react-grid-material-ui'
import { Children, FC, ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames'
import { useContextMenu } from 'widgets/Table/hooks/useContextMenu.tsx'

import cls from './TableTreeCell.module.scss'

export interface TableTreeCellProps extends Omit<TableTreeColumnBase.CellProps, 'column'> {
  isTree: boolean
  childKey: string
  column: TableTreeColumnBase.CellProps['column'] & { render?: (value: any, row: any) => ReactNode }
}

export const TableTreeCell: FC<TableTreeCellProps> = (props) => {
  const { column, row, tableColumn, tableRow, value, children, isTree } = props
  const requiredAttr = { column, row, tableColumn, tableRow, value }
  const isNotChildren = isTree && row?.children?.length === 0

  const { contextMenuItem, handleContextMenu } = useContextMenu()

  if (column?.render) {
    const childrenArr = Children.toArray(children)
    childrenArr.pop()

    return (
      <TableTreeColumn.Cell {...requiredAttr}>
        <button
          type='button'
          onContextMenu={handleContextMenu}
          className={classNames(cls.cellLabelTree, { [cls.isNotChilds]: isNotChildren }, [
            cls.cellLabelTreeContextMenu,
          ])}
        >
          {childrenArr}
          {column?.render(value, row)}
        </button>
        {contextMenuItem(row.name)}
      </TableTreeColumn.Cell>
    )
  }

  return (
    <TableTreeColumn.Cell row={row} tableRow={tableRow} column={column} tableColumn={tableColumn} value={value}>
      <div className={classNames(cls.cellLabelTree, { [cls.isNotChilds]: isNotChildren }, [])}>{children}</div>
    </TableTreeColumn.Cell>
  )
}
