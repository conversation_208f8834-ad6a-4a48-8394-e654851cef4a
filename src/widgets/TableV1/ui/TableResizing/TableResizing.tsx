import { TableColumnResizingProps } from '@devexpress/dx-react-grid'
import { TableColumnResizing } from '@devexpress/dx-react-grid-material-ui'
import { useMemo } from 'react'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

interface TableResizingProps<RowData extends IBaseRowData> {
  columns: IColumn<RowData>[]
  onColumnWidthsChange: TableColumnResizingProps['onColumnWidthsChange']
}

export const TableResizing = <RowData extends IBaseRowData>(props: TableResizingProps<RowData>) => {
  const { columns, onColumnWidthsChange } = props
  const defaultColumnWidths = useMemo(() => columns.map((el) => ({ columnName: el.name, width: el.width })), [columns])

  const columnsResize = useMemo(() => {
    if (!onColumnWidthsChange) return undefined

    return columns.map((el) => ({ columnName: el.name, width: el.width }))
  }, [columns, onColumnWidthsChange])

  return (
    <TableColumnResizing
      defaultColumnWidths={defaultColumnWidths}
      columnWidths={columnsResize}
      onColumnWidthsChange={onColumnWidthsChange}
      resizingMode='nextColumn'
    />
  )
}
