import { Table, VirtualTableProps } from '@devexpress/dx-react-grid-material-ui'
import { classNames } from 'shared/lib/classNames'

import cls from './TableRow.module.scss'

export const TableRow: VirtualTableProps['rowComponent'] = (props) => {
  const { row, children } = props
  const isGrayColor = row?.rowColor === 'gray'
  const isGrayColorAndItalic = row?.rowColor === 'grayAndItalic'
  const isGrayAndBoldColor = row?.rowColor === 'grayAndBold'

  return (
    <Table.Row
      title={row.prompt}
      className={classNames(
        '',
        {
          [cls.grayRow]: isGrayColor,
          [cls.isGrayAndBoldColor]: isGrayAndBoldColor,
          [cls.isGrayColorAndItalic]: isGrayColorAndItalic,
        },
        [],
      )}
      {...props}
    >
      {children}
    </Table.Row>
  )
}
