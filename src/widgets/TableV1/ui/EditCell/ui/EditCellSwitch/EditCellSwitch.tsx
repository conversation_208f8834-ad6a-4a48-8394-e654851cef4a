import { TableInlineCellEditing as TableInlineCellEditingBase } from '@devexpress/dx-react-grid'
import { Table } from '@devexpress/dx-react-grid-material-ui'
import { useEffect } from 'react'
import { Switch } from 'shared/ui/Switch'
import { IBaseRowData } from 'widgets/TableV1'
import { IEditCellColumn } from 'widgets/TableV1/ui'

import cls from './EditCellSwitch.module.scss'

interface EditCellSwitchProps<RowData extends IBaseRowData> extends TableInlineCellEditingBase.CellProps {
  column: IEditCellColumn<RowData>
  row: RowData
}

/**
 * Компонент заглушка. Отрисовывается 1 раз при нажатии на ячейку.
 * В момент монтирования значение меняется на противоположное и после изменения происходит сброс состояние редактирования.
 * @param props
 * @constructor
 */
export const EditCellSwitch = <RowData extends IBaseRowData>(props: EditCellSwitchProps<RowData>) => {
  const { column, value, onBlur, row, tableColumn, tableRow, onValueChange } = props
  const { onAfterChange } = column

  useEffect(() => {
    // применяем новое значение для сохранения в стейте библиотеки dev-express
    onValueChange(!value)
    // применяем новое значение для обработки в callback - onAfterChange при описании колонки
    onAfterChange?.(!value, row)
    // сброс режима редактирования
    onBlur()
  }, [])

  return (
    <Table.Cell column={column} row={row} tableColumn={tableColumn} tableRow={tableRow} value={value}>
      <div className={`${cls.cell} ${cls.editable}`}>
        <Switch checked={value} />
      </div>
    </Table.Cell>
  )
}
