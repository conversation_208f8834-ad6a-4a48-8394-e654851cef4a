import DialogActions from '@mui/material/DialogActions'
import {
  DatePicker as DatePickerMui,
  DatePickerProps as DatePickerPropsMui,
  LocalizationProvider,
} from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { useTheme } from 'app/providers/ThemeProvider'
import { addDays } from 'date-fns'
import { ru } from 'date-fns/locale'
import { Dispatch, SetStateAction, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { ButtonArrow } from 'shared/ui/DatePicker/ui/ButtonArrow'

import cls from './DatePicker.module.scss'
import { ActionBar, TextFieldWithDayWeek, TextFieldWithDayWeekProps } from './ui'

export interface DatePickerProps {
  className?: string
  label?: string
  value?: Date
  setValue?: (date: Date) => void
  isOpen?: boolean
  setIsOpen?: Dispatch<SetStateAction<boolean>>
  disablePast?: boolean
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void
  focused?: boolean
  isArrow?: boolean
  disabled?: boolean
  isDayOfWeek?: boolean
  shouldDisableDate?: (day: Date) => boolean
  onAccept?: (date: Date) => void
  slotProps?: DatePickerPropsMui<Date>['slotProps']
  views?: DatePickerPropsMui<Date>['views']
  format?: string
  error?: string
  useLegacyStyle?: boolean
  /** если есть ошибка и showErrorMsg = true - текст ошибки показывается. по умолчанию поле = true */
  showErrorMsg?: boolean
  handleAllDates?: boolean
}

// INFO: Если имплементировать изменение даты через debounce на уровне DatePicker,
// учесть side эффекты в компонентах его использующих. Например, ломается поведение в TelemetryTable
export const DatePicker = (props: DatePickerProps) => {
  const {
    className,
    label,
    value,
    setValue,
    isOpen,
    setIsOpen,
    disablePast = false,
    onKeyDown,
    focused = false,
    disabled = false,
    shouldDisableDate,
    onAccept,
    isDayOfWeek = false,
    isArrow = false,
    slotProps = {},
    views = ['year', 'month', 'day'],
    format,
    error,
    showErrorMsg = true,
    useLegacyStyle = false,
    handleAllDates = false,
  } = props
  const { theme } = useTheme()

  const [isOpenLocal, setIsOpenLocal] = useState(false)
  const [nullValue, setNullValue] = useState(false)

  const datePickerRef = useRef<HTMLInputElement>(null)

  const handleDateNavigation = (type: 'day' | 'week', direction: 'plus' | 'minus') => {
    if (disabled || !value || !setValue) return

    const daysToAdd = type === 'day' ? 1 : 7
    const adjustedDays = direction === 'plus' ? daysToAdd : -daysToAdd
    const newDate = addDays(value, adjustedDays)

    setValue(newDate)
  }

  const calendarActions = () => (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
      <div
        className={classNames(cls.dateContainer, { [cls.legacyDateContainer]: useLegacyStyle }, [])}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !nullValue) {
            if (setIsOpen) {
              setIsOpen(false)
            } else {
              setIsOpenLocal(false)
              if (onAccept && value) {
                onAccept(value)
              }
            }
          }
        }}
      >
        {isArrow && (
          <>
            <ButtonArrow
              disabled={disabled}
              onClick={() => handleDateNavigation('week', 'minus')}
              iconName='backDoubleArrowForDatePicker'
              iconSize={useLegacyStyle ? 12 : undefined}
            />
            <ButtonArrow
              disabled={disabled}
              onClick={() => handleDateNavigation('day', 'minus')}
              iconName='backArrowForDatePicker'
              iconSize={useLegacyStyle ? 12 : undefined}
            />
          </>
        )}
        <DatePickerMui
          inputRef={datePickerRef}
          autoFocus={focused}
          disablePast={disablePast}
          value={nullValue ? null : value}
          disabled={disabled}
          views={views}
          shouldDisableDate={(day) => (shouldDisableDate ? shouldDisableDate(day) : false)}
          format={format}
          onChange={(newValue, context) => {
            // Обработчик для всех дат
            if (handleAllDates && setValue) {
              setValue(newValue as Date)
            }
            // Если новая дата невалидна, не обновлять value
            if (context.validationError) return
            setNullValue(false)
            if (setValue && newValue) {
              setValue(newValue)
            }
          }}
          onAccept={(value) => onAccept && value && onAccept(value)}
          open={isOpen || isOpenLocal}
          label={label}
          className={classNames(
            cls.datePicker,
            {
              [cls.isDayOfWeek]: isDayOfWeek,
              [cls.datePickerError]: error,
            },
            className ? [className] : [],
          )}
          onOpen={() => {
            if (setIsOpen) {
              setIsOpen(true)
            } else {
              setIsOpenLocal(true)
            }
            setTimeout(() => {
              const root = document.querySelector('.MuiPickersPopper-root')
              if (root) {
                root.classList.add(theme)
              }
            }, 100)
          }}
          onClose={() => {
            if (setIsOpen) {
              setIsOpen(false)
            } else {
              setIsOpenLocal(false)
            }
            setNullValue(false)
            setTimeout(() => {
              if (datePickerRef?.current) {
                datePickerRef.current.focus()
              }
            }, 100)
          }}
          slots={{
            actionBar: (props) => <ActionBar {...props} disabled={disabled} setNullValue={setNullValue} />,
            textField: TextFieldWithDayWeek,
          }}
          slotProps={{
            ...slotProps,
            actionBar: {
              actions: ['clear', 'today'],
            },
            textField: {
              onKeyDown,
              disabled,
              isDayOfWeek,
              ...slotProps?.textField,
            } as TextFieldWithDayWeekProps,
          }}
        />
        {isArrow && (
          <>
            <ButtonArrow
              disabled={disabled}
              onClick={() => handleDateNavigation('day', 'plus')}
              iconName='arrowForDatePicker'
              iconSize={useLegacyStyle ? 12 : undefined}
            />
            <ButtonArrow
              disabled={disabled}
              onClick={() => handleDateNavigation('week', 'plus')}
              iconName='doubleArrowForDatePicker'
              iconSize={useLegacyStyle ? 12 : undefined}
            />
          </>
        )}
      </div>
    </LocalizationProvider>
  )

  return (
    <DialogActions className={cls.dialogActions}>
      {calendarActions()}
      {error && showErrorMsg && <div className={cls.errorMessage}>{error}</div>}
    </DialogActions>
  )
}
