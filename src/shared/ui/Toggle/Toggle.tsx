import ToggleButton from '@mui/material/ToggleButton'
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup'
import { ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'

import cls from './Toggle.module.scss'

interface IItemsProps {
  label: string | ReactNode | null
  value: string | number
}

interface ToggleProps<T> {
  items?: ReadonlyArray<IItemsProps>
  value: T
  setValue: (value: T) => void
}

export const Toggle = <T,>(props: ToggleProps<T>) => {
  const { items = [], value, setValue } = props

  return (
    <ToggleButtonGroup
      value={value}
      exclusive
      sx={{
        color: 'var(--primary-color)',
      }}
      onChange={(_, newValue) => {
        if (newValue !== null) {
          setValue(newValue)
        }
      }}
      aria-label='Platform'
      className={classNames(cls.Toggle, {}, [])}
    >
      {items.map((el: IItemsProps, index: number) => {
        return (
          <ToggleButton key={`toggle-button-${index}`} value={el.value}>
            {el.label}
          </ToggleButton>
        )
      })}
    </ToggleButtonGroup>
  )
}
