import { Event } from '@mui/icons-material'
import EventBusyIcon from '@mui/icons-material/EventBusy'
import TodayIcon from '@mui/icons-material/Today'
import { IconButton, Tooltip } from '@mui/material'
import DialogActions from '@mui/material/DialogActions'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import { type PickerChangeHandlerContext } from '@mui/x-date-pickers/internals/hooks/usePicker/usePickerValue.types'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { PickersActionBarProps } from '@mui/x-date-pickers/PickersActionBar'
import { type DateRange, type DateRangeValidationError } from '@mui/x-date-pickers-pro'
import {
  DateRangePicker as MuiDateRangePicker,
  type DateRangePickerProps,
} from '@mui/x-date-pickers-pro/DateRangePicker'
import { SingleInputDateRangeField } from '@mui/x-date-pickers-pro/SingleInputDateRangeField'
import ru from 'date-fns/locale/ru'
import { useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'

import s from './DateRangePicker.module.scss'

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface DatePickerContext extends PickerChangeHandlerContext<DateRangeValidationError> {}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface DataPickerValue extends DateRange<Date> {}

interface IProps extends DateRangePickerProps<Date> {
  dateFrom: Date | null
  dateTo: Date | null
  handleChangeDate: (value: DataPickerValue, context: DatePickerContext) => void
  error?: string
}

export type NullableDate = Date | null

export const DateRangePicker = (props: IProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const { dateFrom, dateTo, handleChangeDate, maxDate, minDate, error } = props
  const [nullValue, setNullValue] = useState(false)
  const [dates, setDates] = useState<[NullableDate, NullableDate]>([dateFrom, dateTo])
  // const [value, setValue] = useState<DateRange<any>>([dateFrom, dateTo])
  const [context, setContext] = useState<PickerChangeHandlerContext<DateRangeValidationError>>({
    validationError: [null, null],
  })

  // Синхронизация внутреннего state с пропами
  useEffect(() => {
    setDates([dateFrom, dateTo])
  }, [dateFrom, dateTo])

  const onAccept = (value: DateRange<Date>) => {
    let startDate = value[0]
    let endDate = value[1]
    if (startDate && minDate && startDate < minDate) {
      startDate = dateFrom
    }
    if (endDate && maxDate && endDate > maxDate) {
      endDate = dateTo
    }

    setDates([startDate, endDate])
    handleChangeDate([startDate, endDate], context)
  }

  function CustomActionBar(props: PickersActionBarProps) {
    const { onSetToday, actions, className } = props

    if (actions === null || actions?.length === 0) {
      return null
    }

    const calendarActions = (
      <>
        <Tooltip title='Сегодня'>
          <IconButton
            data-mui-test='today-action-button'
            onClick={() => {
              if (dates[0] === null && dates[1] === null) {
                setDates([new Date(), new Date()])
                onSetToday()
                onAccept([new Date(), new Date()])
              }
              if (dates[0] === null && dates[1] !== null) {
                onAccept([new Date(), dates[1]])
                setDates([new Date(), dates[1]])
              }
              if (dates[1] === null && dates[0] !== null) {
                onAccept([dates[0], new Date()])
                setDates([dates[0], new Date()])
              }
              setIsOpen(false)
              setNullValue(false)
            }}
            key='today'
          >
            <TodayIcon className={s.ActionButton} color='primary' />
          </IconButton>
        </Tooltip>
        <Tooltip title='Сбросить'>
          <IconButton
            data-mui-test='clear-action-button'
            onClick={() => {
              setNullValue(true)
              setDates([null, null])
            }}
            key='clear'
          >
            <EventBusyIcon className={s.ActionButton} sx={{ color: 'rgb(208, 20, 20)' }} />
          </IconButton>
        </Tooltip>
      </>
    )

    return <DialogActions className={className}>{calendarActions}</DialogActions>
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
      <div
        onKeyDown={(e) => {
          if (e.code === 'Enter' && !nullValue) {
            setIsOpen(false)
          }
        }}
      >
        <DemoContainer components={['SingleInputDateRangeField']}>
          <MuiDateRangePicker
            maxDate={maxDate}
            minDate={minDate}
            className={classNames(s.dateRangePicker, { [s.dateRangePickerError]: error })}
            slots={{
              field: SingleInputDateRangeField,
              actionBar: CustomActionBar,
            }}
            value={dates as DateRange<Date>}
            defaultValue={[dateFrom, dateTo]}
            onChange={(newValue, context) => {
              setDates(newValue)
              setContext(context)
            }}
            onAccept={onAccept}
            open={isOpen}
            onClose={() => {
              setIsOpen(false)
              setNullValue(false)
            }}
            slotProps={{
              actionBar: {
                actions: ['clear', 'today'],
              },
              textField: {
                InputProps: {
                  endAdornment: (
                    <IconButton className={s.RangePickerIcon} onClick={() => setIsOpen((prev: boolean) => !prev)}>
                      <Event />
                    </IconButton>
                  ),
                },
              },
            }}
          />
        </DemoContainer>
      </div>
    </LocalizationProvider>
  )
}
