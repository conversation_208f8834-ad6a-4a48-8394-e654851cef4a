import {
  getDepPlantsOutput,
  ISaveDepPlantsInput,
  IWerRestriction,
  IWerRestrictionInput,
  IWerRestrictionOptionsOutput,
} from 'entities/api/calcModelPage.entities.ts'
import {
  CharacteristicsSpreadsheetChanges,
  ICalcAllRunningTimesParams,
  ICalcModelCascadeDto,
  ICalcModelPlantPostRunningTimesParams,
  ICalcModelPlantRunningTimesDto,
  ICalcRunningTimeResultsDto,
  ICalcSingleRunningTimeParams,
  ICalcSingleRunningTimeResultDto,
  IDownstreamPlant,
  IPlantCascades,
  IPlantReferenceData,
  IReservoirVolumeSettings,
  IReservoirVolumeTableWithSettingsResponse,
  IReservoirVolumeVbLevelIndicatorsResponse,
  ISpecificConsumptionSettings,
  ISpecificConsumptionTableWithSettingsResponse,
  ITailraceSettings,
  ITailraceTableWithSettingsResponse,
} from 'entities/api/calcModelWerManager.entities'
import { ICalcModelPlant } from 'entities/store/calcModelStore.entities.ts'
import { axiosInstance as api } from 'shared/lib/axios'

// Получение списка станций
export const getPlanPlants = (sortByOrder: boolean, date: string, signal?: AbortSignal): Promise<ICalcModelPlant[]> => {
  return api.get(`/api/v1/wer-model/plants?sortByOrder=${sortByOrder}&date=${date}`, { signal })
}

// Добавление и удаление просматриваемых станций
export const saveDepPlants = (input: ISaveDepPlantsInput): Promise<void> => {
  return api.patch(`/api/v1/wer-model/look-plants`, input)
}

// Сохранение порядка сортировки станций
export const saveCustomSort = (res: number[]): Promise<void> => {
  return api.post(`/api/v1/wer-model/plants/order`, res)
}

// Получение иерархии ДЦ и станций с признаками ДЦ(ВЭР) и просматриваемых ВЭР
export const getDepPlants = (): Promise<getDepPlantsOutput[]> => {
  return api.get(`/api/v1/wer-model/dep-plants`)
}

// Получение списка ограничений станции на определённую дату и период
export const getRestrictions = (
  plantId: number,
  date: string,
  periodBegin?: string,
  periodEnd?: string,
): Promise<IWerRestriction[]> => {
  return api.get(`/api/v1/plant/${plantId}/restrictions`, {
    params: { date, periodBegin, periodEnd },
  })
}

// Получение возможных значений полей в ограничениях станций
export const getRestrictionOptions = (): Promise<IWerRestrictionOptionsOutput> => {
  return api.get('/api/v1/plant/restrictions/options')
}

// Добавление, редактирование и удаление ограничений станции на определённую дату
// и получение списка ограничений на определённый период
export const saveRestrictions = (
  plantId: number,
  date: string,
  payload: IWerRestrictionInput,
  periodBegin?: string,
  periodEnd?: string,
): Promise<IWerRestriction[]> => {
  return api.post(`/api/v1/plant/${plantId}/restrictions`, payload, {
    params: { date, periodBegin, periodEnd },
  })
}

// Получение справочных данных станции на определенную дату
export const getPlantReferenceData = (plantId: number, params: { date: string }): Promise<IPlantReferenceData[]> => {
  return api.get(`/api/v1/plant/${plantId}/reference-data`, { params })
}

// Получение информации о связях станции с каскадами и другими станциями
export const getPlantCascades = (plantId: number): Promise<IPlantCascades> => {
  return api.get(`/api/v1/cascades/plant/${plantId}`)
}

// Сохранение информации о связях станции с каскадами и другими станциями
export const savePlantCascades = (data: IPlantCascades): Promise<void> => {
  return api.put(`/api/v1/cascades/plant/`, data)
}

// Получение характеристики объёмов водохранилища с настройками
export const getReservoirVolumeCharacteristics = (
  plantId: number,
  date: string,
): Promise<IReservoirVolumeTableWithSettingsResponse> => {
  return api.get(`/api/v1/wer-model/reservoir-volume`, { params: { plantId, date } })
}

// Получение текущего индикатора уровня ВБ для характеристики объёмов водохранилища со всеми возможными уровнями
export const getReservoirVolumeIndicatorVbLevel = (params: {
  plantId: number
}): Promise<IReservoirVolumeVbLevelIndicatorsResponse> => api.get(`/api/v1/indicator/headrace`, { params })

// Обновление значения текущего индикатора уровня ВБ для характеристики объёмов водохранилища
export const updateReservoirVolumeIndicatorVbLevel = (params: {
  plantId: number
  indicatorId: number
}): Promise<IReservoirVolumeVbLevelIndicatorsResponse> => api.put(`/api/v1/indicator/headrace`, params)

// Сохранение настройки характеристики объёмов водохранилища
export const saveReservoirVolumeSettings = (
  plantId: number,
  date: string,
  settings: IReservoirVolumeSettings,
): Promise<void> => {
  return api.post(`/api/v1/wer-model/reservoir-volume/settings`, settings, { params: { plantId, date } })
}

// Сохранение характеристики объёмов водохранилища
export const saveReservoirVolumeCharacteristics = (
  plantId: number,
  date: string,
  changes: CharacteristicsSpreadsheetChanges,
): Promise<void> => {
  return api.post(`/api/v1/wer-model/reservoir-volume`, changes, { params: { plantId, date } })
}

// Получение списка нижележащих станций с включенной характеристикой 'Подпор'
export const getDownstreamPlantList = (plantId: number, date: string): Promise<IDownstreamPlant[]> => {
  return api.get(`/api/v1/wer-model/tailrace/downstream-plants`, { params: { plantId, date } })
}

// Получение характеристики уровня нижнего бьефа с настройками
export const getTailraceCharacteristics = (
  plantId: number,
  date: string,
  downstreamPlantId?: number,
): Promise<ITailraceTableWithSettingsResponse> => {
  return api.get(`/api/v1/wer-model/tailrace`, { params: { plantId, date, downstreamPlantId } })
}

// Сохранение настройки характеристики уровня нижнего бьефа
export const saveTailraceSettings = (plantId: number, date: string, settings: ITailraceSettings): Promise<void> => {
  return api.post(`/api/v1/wer-model/tailrace/settings`, settings, { params: { plantId, date } })
}

// Сохранение характеристики уровня нижнего бьефа
export const saveTailraceCharacteristics = (
  plantId: number,
  date: string,
  payload: CharacteristicsSpreadsheetChanges,
): Promise<void> => {
  return api.post(`/api/v1/wer-model/tailrace`, payload, { params: { plantId, date } })
}

// Запрос фактических измерений для уровня нижнего бьефа
export const getTailraceActualMeasurements = (
  plantId: number,
  date: string,
  startDate: string,
  endDate: string,
  signal?: AbortSignal,
): Promise<number[][]> => {
  return api.get(`/api/v1/wer-model/tailrace/actual-measurements`, {
    params: { plantId, date, startDate, endDate },
    signal,
  })
}

// Получение характеристики удельного расхода ГЭС с настройками
export const getSpecificConsumptionCharacteristics = (
  plantId: number,
  date: string,
): Promise<ISpecificConsumptionTableWithSettingsResponse> => {
  return api.get(`/api/v1/wer-model/specific-consumption`, { params: { plantId, date } })
}

// Сохранение настройки характеристики удельного расхода ГЭС
export const saveSpecificConsumptionSettings = (
  plantId: number,
  date: string,
  settings: ISpecificConsumptionSettings,
): Promise<void> => {
  return api.post(`/api/v1/wer-model/specific-consumption/settings`, settings, { params: { plantId, date } })
}

// Сохранение характеристики удельного расхода ГЭС
export const saveSpecificConsumptionCharacteristics = (
  plantId: number,
  date: string,
  changes: CharacteristicsSpreadsheetChanges,
): Promise<void> => {
  return api.post(`/api/v1/wer-model/specific-consumption`, changes, { params: { plantId, date } })
}

// Запрос фактических измерений
export const getSpecificConsumptionActualMeasurements = (
  plantId: number,
  date: string,
  startDate: string,
  endDate: string,
  signal?: AbortSignal,
): Promise<number[][]> => {
  return api.get(`/api/v1/wer-model/specific-consumption/actual-measurements`, {
    params: { plantId, date, startDate, endDate },
    signal,
  })
}

// Получение перечня каскадов
export const getListOfCascades = (params: { activeOnly: boolean }): Promise<ICalcModelCascadeDto[]> =>
  api.get(`/api/v1/wer-model/cascades`, { params })

// Получение списка времен добегания
export const getRunningTimes = (): Promise<ICalcModelPlantRunningTimesDto[]> =>
  api.get(`/api/v1/wer-model/running-times`)

export const updateRunningTimes = (
  data: ICalcModelPlantPostRunningTimesParams[],
): Promise<ICalcModelPlantRunningTimesDto[]> => api.post(`/api/v1/wer-model/running-times`, data)

export const calcRunningTime = (data: ICalcSingleRunningTimeParams): Promise<ICalcSingleRunningTimeResultDto[]> =>
  api.post(`/api/v1/wer-model/running-times/calculate`, data)

export const calcAllRunningTimes = (data: ICalcAllRunningTimesParams): Promise<ICalcRunningTimeResultsDto> =>
  api.post(`/api/v1/wer-model/running-times/calculate/all`, data)
