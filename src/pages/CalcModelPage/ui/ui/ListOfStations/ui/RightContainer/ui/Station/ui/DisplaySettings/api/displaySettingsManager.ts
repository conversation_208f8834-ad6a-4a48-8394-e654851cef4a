import { axiosInstance as api } from 'shared/lib/axios'

// все возможные коды колонок
export type ColumnKey =
  | 'RESULT_MIN'
  | 'P_GEN'
  | 'RESULT_MAX'
  | 'P_SOURCE'
  | 'RESERVES_MAX'
  | 'AVRCHM_LOAD'
  | 'AVRCHM_UNLOAD'
  | 'NPRCH'
  | 'LIMIT_MIN'
  | 'LIMIT_MAX'
  | 'CM_P_MIN'
  | 'CM_P_MAX'
  | 'MODES_P_MIN'
  | 'MODES_P_MAX'
  | 'MODES_DECLARED'
  | 'CONSUMPT'

// разделы
export type Section = 'PLANT' | 'SUMMARY'

// вложенный заголовок
export interface DisplaySettingsSubColumnDto {
  title: string
}

// описание колонки в шапке таблички
export interface DisplaySettingsColumnDto {
  title: string
  subColumns: DisplaySettingsSubColumnDto[]
}

// ячейка с флагом видимости
export interface DisplaySettingsCellDto {
  visible: boolean
  column: ColumnKey
  section: Section
}

// строка настроек
export interface DisplaySettingsRowDto {
  title: string
  cells: DisplaySettingsCellDto[]
}

// ответ сервера по getDisplaySettings
export interface DisplaySettingsResponse {
  columns: DisplaySettingsColumnDto[]
  rows: DisplaySettingsRowDto[]
}

// один элемент массива columns
export interface HiddenColumnCellDto {
  /** необязательно, здесь можно пояснение к колонке */
  description?: string
  /** можно ли редактировать эту ячейку */
  editable?: boolean
  /** отображать ли колонку */
  visible: boolean
  /** код колонки */
  column: ColumnKey
  /** где показывать */
  section: Section
}

// тело запроса на сохранение
export interface HiddenColumnsRequestDto {
  /** идентификатор станции */
  plantId: number
  /** дата начала действия в формате 'YYYY-MM-DD' */
  date: string
  /** список колонок с флагами */
  columns: HiddenColumnCellDto[]
}

// Получение таблицы настройки отображения колонок на определённую дату
export const getDisplaySettings = (plantId: number, date: string): Promise<DisplaySettingsResponse> => {
  return api.get(`/api/v1/plant/${plantId}/hidden-columns`, { params: { date } })
}

// Сохранение настроек отображения колонок на определённую дату
export const saveDisplaySettings = (settings: HiddenColumnsRequestDto): Promise<void> => {
  return api.put(`/api/v1/plant/hidden-columns`, settings)
}
