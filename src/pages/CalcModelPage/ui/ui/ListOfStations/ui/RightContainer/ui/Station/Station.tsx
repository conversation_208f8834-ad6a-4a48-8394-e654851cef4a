import AddIcon from '@mui/icons-material/Add'
import { I<PERSON><PERSON><PERSON><PERSON>, Tooltip } from '@mui/material'
import {
  ExValueConfigsOutput,
  IParametersConfigsOutput,
  IRestriction,
  ISaveParamsInput,
  ValueConfigsOutput,
  ValueConfigsOutputWithGroups,
} from 'entities/api/calcModelPage.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { observer } from 'mobx-react'
import { AddGroupsModal } from 'pages/CalcModelPage/ui/ui/AddGroupsModal'
import { IObj } from 'pages/CalcModelPage/ui/ui/AddGroupsModal/ui/AddGroupsModal.tsx'
import { DeleteModal } from 'pages/CalcModelPage/ui/ui/DeleteModal'
import { HistoryParamsModal } from 'pages/CalcModelPage/ui/ui/HistoryParamsModal'
import { LimitModal } from 'pages/CalcModelPage/ui/ui/LimitModal'
import { validate } from 'pages/CalcModelPage/ui/ui/ListOfStations/lib/Validate.ts'
import cls from 'pages/CalcModelPage/ui/ui/ListOfStations/ListOfStations.module.scss'
import { GroupsList } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/GroupsList'
import { IItem } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/GroupsList/GroupsList.tsx'
import { IPropsRight } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames'
import { prepareFlatData } from 'shared/lib/prepareData'
import { Button } from 'shared/ui/Button'
import { Icon } from 'shared/ui/Icon'
import { Loader } from 'shared/ui/Loader'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { Toggle } from 'shared/ui/Toggle'
import { IPrepareRgu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore.ts'
import { CustomTableCell, Table } from 'widgets/Table'

import { WarningsModal } from './ui'
import { DisplaySettings } from './ui/DisplaySettings'

type TKeysForItem = 'E_MAX_E_MIN' | 'REGULATED_UNIT' | 'CONSUMPTION_SCHEDULE_BINDING' | 'RGU_GROUP'

type TItem<T> = T extends TKeysForItem
  ? IParametersConfigsOutput<string>
  : IParametersConfigsOutput<ValueConfigsOutputWithGroups>

interface ILimitModal {
  mode: string
  restrictions: IRestriction[]
}

interface IHistoryModal {
  type?: string
  id?: number
  plantId?: number
  parameterName?: string
  name?: string
  generatorName?: string
  rguName?: string
  description?: string
}

type ICreateGroup = object

interface IRow extends Omit<Omit<IPrepareRgu, 'children'>, 'generators'> {
  name: string
  generators: string[]
  tabId: string
  isEdit: boolean
  id: number
  children: IRow[]
  avrchm: boolean
  voltage: string
}

const voltages = [
  { value: '', label: '' },
  { value: '35', label: '35' },
  { value: '110', label: '110' },
  { value: '150', label: '150' },
  { value: '220', label: '220' },
  { value: '330', label: '330' },
  { value: '500', label: '500' },
  { value: '750', label: '750' },
]

const characteristicsList = [
  { value: 'main', label: 'Основные характеристики' },
  { value: 'extra', label: 'Дополнительные характеристики' },
  { value: 'displaySettings', label: 'Настройки отображения' },
] as const

type TabKey = (typeof characteristicsList)[number]['value']

export const Station = observer((props: IPropsRight) => {
  const { calcModelStore } = useStore()
  const {
    plants,
    trashDepPlants,
    getParams,
    params,
    configs,
    energyDistrictForParams,
    saveParams,
    typeStation,
    date,
    actualStage,
  } = calcModelStore

  const {
    editMode,
    setEditMode,
    rows,
    setRows,
    isLoading,
    setIsLoading,
    errors,
    setErrors,
    isLoadingTable,
    setIsLoadingTable,
    finalPlants,
    selectLeftMenu,
    isEditRows,
    init,
    editModeRole,
  } = props
  const [isLimitModal, setIsLimitModal] = useState<null | ILimitModal>(null)
  const [isHistoryModal, setIsHistoryModal] = useState<null | IHistoryModal>(null)
  const [createGroup, setCreateGroup] = useState<null | ICreateGroup>(null)
  const [deleteModal, setDeleteModal] = useState<boolean | null>(null)
  const [characteristics, setCharacteristics] = useState<TabKey>('main')

  const selectItem = selectLeftMenu ? finalPlants.find((el) => el.value === selectLeftMenu) : null
  const deleteItem = deleteModal ? finalPlants.find((el) => el.value === (deleteModal as unknown as number)) : null

  const [adjustableUnit, setAdjustableUnit] = useState<string>('PLANT')
  const [optimization, setOptimization] = useState<string | boolean>('true')

  const isEditPlant = plants?.find((el) => el?.value === selectLeftMenu)?.icon === 'settings' && actualStage?.code
  const isPastDate = useMemo(() => date <= new Date(), [date])

  const adjustableUnitList = [
    { value: 'PLANT', label: 'Электростанция' },
    { value: 'RGU', label: 'РГЕ' },
    { value: 'GENERATOR', label: 'Гидрогенератор', hidden: true },
  ]

  const optimizationList = [
    { value: 'true', label: 'Оптимизируемая' },
    { value: 'false', label: 'Неоптимизируемая' },
  ]

  const columns = [
    {
      name: 'name',
      title: 'Конфигурация',
      width: 300,
      render: (value: string, row: IRow) => {
        const isGenerators = !!row.generators

        return (
          <div className={cls.NameConfig}>
            <div className={cls.IconConfig}>
              {isGenerators ? <Icon width={16} name='rge' /> : <Icon width={16} name='generator' />}
            </div>
            {value}
          </div>
        )
      },
    },
    {
      name: 'voltage',
      title: 'Напряжение, кВ',
      width: 300,
      editingEnabled: (isEditPlant && editModeRole) || !isPastDate,
      editType: 'select',
      chooseItems: voltages,
    },
    {
      name: 'startDate',
      title: 'Дата начала',
      width: 175,
      editingEnabled: false,
      editType: 'date',
    },
    {
      name: 'endDate',
      title: 'Дата окончания',
      width: 175,
      editingEnabled: false,
      editType: 'date',
    },
    {
      name: 'avrchm',
      title: 'Участие в АВРЧМ',
      width: 200,
      headRender: () => {
        return <div className={cls.ActionHeader}>Участие в АВРЧМ</div>
      },
      render: (value: boolean, row: IRow) => {
        const handleChangeSwitch = (_: unknown, checked: boolean) => {
          const editValue = (arr: IPrepareRgu[], parent?: boolean): IPrepareRgu[] => {
            return arr.map((item) =>
              item.tabId === row.tabId
                ? {
                    ...item,
                    isEdit: parent !== null ? parent : false,
                    avrchm: parent !== null && !parent ? false : checked,
                    ...(item.children?.length && {
                      children: item.children.map((i) => ({
                        ...i,
                        isEdit: true,
                        avrchm: checked,
                      })),
                    }),
                  }
                : item.children?.length
                  ? {
                      ...item,
                      children: editValue(item.children, item.avrchm),
                    }
                  : item,
            )
          }

          const newRows = editValue(rows).map((item) => ({
            ...item,
            avrchm: item?.children ? item?.children?.some((i) => i.avrchm) : false,
          }))
          setRows(newRows)
        }

        return (
          <div className={cls.ActionCell}>
            <Button
              onClick={() => {
                const isRgu = row?.generators && row?.generators?.length > 0
                setIsHistoryModal({
                  type: isRgu ? 'rgu' : 'generator',
                  id: row.id,
                  generatorName: isRgu ? undefined : row.name,
                  rguName: isRgu ? row.name : undefined,
                  parameterName: 'PARTICIPATION_AVRCHM',
                  name: 'Участие в АВРЧМ',
                })
              }}
              variant='text'
              className={classNames(
                cls.ButtonInfo,
                {
                  [cls.HiddenButton]: row?.generators && row?.generators?.length > 0,
                },
                [cls.ButtonHistory],
              )}
            >
              <Icon width={14} name='history' />
            </Button>
            <CustomTableCell>
              <Switch
                checked={value}
                onChange={(_: unknown, checked: boolean) => {
                  handleChangeSwitch(_, checked)
                }}
                disabled={!(isEditPlant && editModeRole) || isPastDate}
              />
            </CustomTableCell>
          </div>
        )
      },
    },
    {
      name: 'priorityLoad',
      title: 'Приоритетная загрузка',
      width: 200,
      headRender: () => {
        return <div className={cls.ActionHeader}>Приоритетная загрузка</div>
      },
      render: (value: boolean, row: IRow) => {
        const isGenerators = !!row.generators
        const handleChangeSwitch = (_: unknown, checked: boolean) => {
          const editValue = (arr: IPrepareRgu[]) => {
            return arr.map((item) =>
              item.tabId === row.tabId
                ? {
                    ...item,
                    isEdit: prepareData(configs, 0)?.find((i) => i.id === row.id)?.priorityLoad !== checked,
                    priorityLoad: checked,
                  }
                : item,
            )
          }
          const newRows = editValue(rows)
          setRows(newRows)
        }

        return (
          isGenerators && (
            <div className={cls.ActionCell}>
              <Button
                onClick={() => {
                  const isRgu = row?.generators && row?.generators?.length > 0
                  setIsHistoryModal({
                    type: isRgu ? 'rgu' : 'generator',
                    id: row.id,
                    parameterName: 'PRIORITY_LOAD',
                    name: 'Приоритетная загрузка',
                    generatorName: isRgu ? undefined : row.name,
                    rguName: isRgu ? row.name : undefined,
                  })
                }}
                variant='text'
                className={classNames(cls.ButtonInfo, {}, [cls.ButtonHistory])}
              >
                <Icon width={14} name='history' />
              </Button>
              <CustomTableCell>
                <Switch
                  checked={value}
                  onChange={handleChangeSwitch}
                  disabled={!(isEditPlant && editModeRole) || isPastDate}
                />
              </CustomTableCell>
            </div>
          )
        )
      },
    },
  ]

  const prepareData = (arr: IPrepareRgu[], parentId: number): IPrepareRgu[] => {
    return arr.map((el) => {
      const children = el?.children && el?.children?.length > 0 ? prepareData(el?.children, el.id) : []

      return { ...el, tabId: `${parentId}-${el.id}`, children }
    })
  }

  const [expandedRowIds, setExpandedRowIds] = useState<(string | null)[]>([])
  const [hiddenColumns, setHiddenColumns] = useState<string[]>([])

  const initialRows = useMemo(() => {
    const rows = prepareData(configs, 0)
    setRows(rows)
    setTimeout(() => {
      setIsLoading(false)
    }, TIME_LOADER)
    setTimeout(() => {
      setIsLoadingTable(false)
    }, TIME_LOADER * 2)

    return rows
  }, [configs])

  // При первоначальной загрузке развернуть все строки
  useEffect(() => {
    if (initialRows.length > 0) {
      const allTabIds = initialRows.map((el) => el?.tabId ?? null)
      setExpandedRowIds(allTabIds)
    }
  }, [initialRows])

  useEffect(() => {
    if (typeStation === 'GES') {
      setHiddenColumns([])
    } else {
      setHiddenColumns(['avrchm', 'priorityLoad'])
    }
  }, [typeStation])

  useEffect(() => {
    if (selectItem && date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
      const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`
      const prepareDate = `${year}-${month}-${day}`
      getParams(selectItem.plantId, prepareDate)
    }
  }, [selectItem])

  const [paramsFinal, setParamsFinal] = useState<IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]>([])
  const [efficiency, setEfficiency] = useState('1')

  useEffect(() => {
    if (params.length > 0) {
      const curAdjustableUnit = params.find((el) => el.parameterName === 'REGULATED_UNIT')
      if (curAdjustableUnit) {
        setAdjustableUnit(curAdjustableUnit?.parameterValue?.value as unknown as string)
      }
      const curOptimization = params.find((el) => el.parameterName === 'OPTIMIZATION')
      if (curOptimization) {
        setOptimization(curOptimization.parameterValue.value.turnedOn)
      }
      const curEfficiency = params.find((el) => el.parameterName === 'EFFICIENCY')
      if (curEfficiency) {
        setEfficiency(curEfficiency.parameterValue.value as unknown as string)
      }
      const res = params
        .filter(
          (el) =>
            el.parameterName !== 'REGULATED_UNIT' &&
            el.parameterName !== 'OPTIMIZATION' &&
            el.parameterName !== 'EFFICIENCY',
        )
        .sort((a, b) => a.priority - b.priority)
      setParamsFinal(res as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[])
    }
  }, [params])

  const regulatedUnits = [
    { value: 'PLANT', label: 'Станция' },
    { value: 'GENERATOR', label: 'Генератор' },
    { value: 'RGU', label: 'РГЕ' },
  ]

  const changeValue = (type: string, value: ValueConfigsOutput, subType: string) => {
    setParamsFinal((prev) => {
      return prev.map((item) => {
        if (item.parameterName === type) {
          const { parameterValue } = item
          if (
            type === 'PARTICIPATION_NPRCH' ||
            type === 'PARTICIPATION_AVRCHM' ||
            type === 'RGU_GROUP' ||
            type === 'PRIORITY_LOAD_RGU' ||
            type === 'EFFICIENCY' ||
            type === 'E_MAX_E_MIN' ||
            type === 'TERTIARY_RESERVE'
          ) {
            return {
              ...item,
              parameterValue: { ...parameterValue, value: { ...parameterValue.value, value } },
              isEdit: true,
            }
          }
          if (type === 'PRESSURE_RECESSION_WATCH' || type === 'LOAD_UNLOAD_SPEED') {
            return {
              ...item,
              parameterValue: {
                ...parameterValue,
                value: { ...parameterValue.value, value: { ...parameterValue.value.value, [subType]: value } },
              },
              isEdit: true,
            }
          }
          if (item.parameterName === 'MAXIMUM_LIMIT' || item.parameterName === 'MINIMUM_LIMIT') {
            return {
              ...item,
              parameterValue: {
                ...parameterValue,
                value: { ...parameterValue.value, value: { ...parameterValue?.value?.value, actualLimit: value } },
              },
              isEdit: true,
            }
          }
          if (item.parameterName === 'CONSUMPTION_SCHEDULE_BINDING') {
            return {
              ...item,
              parameterValue: { ...parameterValue, value: { ...parameterValue.value, value: { id: value } } },
              isEdit: true,
            }
          }

          return { ...item, parameterValue: { ...parameterValue, value }, isEdit: true }
        }

        return item
      }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
    })
  }

  const [deleteGroup, setDeleteGroup] = useState<IItem | null>(null)

  const getExtraContent = (type: string, item: TItem<string>) => {
    const finVoltages = voltages.filter((el) => {
      return rows.some((item) => Number(item.voltage) === Number(el.value))
    })
    switch (type) {
      case 'E_MAX_E_MIN':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.EminMax}
                label='Абсолютное значение, млн. кВт*ч'
                type='number'
                toFixed={3}
                positiveNumber
                value={item?.parameterValue?.value?.value && String(item?.parameterValue?.value?.value)}
                onChange={(e) => {
                  const value = e.target.value
                  changeValue('E_MAX_E_MIN', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case 'REGULATED_UNIT':
        return (
          <>
            <Select
              variant='outlined'
              className={cls.Ter}
              items={regulatedUnits}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              value={item?.parameterValue?.value?.value ? (item?.parameterValue?.value as unknown as string) : 'PLANT'}
              onChange={(value) => {
                changeValue('REGULATED_UNIT', value, '')
                setEditMode(true)
              }}
            />
          </>
        )
      case 'OPTIMIZATION':
        return <></>
      case 'CONSUMPTION_SCHEDULE_BINDING':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <Select
                disabled={!isEditPlant || !editModeRole || isPastDate}
                variant='outlined'
                className={cls.Ter}
                items={
                  energyDistrictForParams.some((el) => el.id === item?.parameterValue?.value?.value?.id)
                    ? energyDistrictForParams
                    : item?.parameterValue?.value?.value?.id && item?.parameterValue?.value?.value?.name
                      ? [
                          {
                            ...item.parameterValue.value.value,
                            label: item.parameterValue.value.value.name,
                            value: item.parameterValue.value.value.id,
                          },
                          ...energyDistrictForParams,
                        ]
                      : energyDistrictForParams
                }
                value={item?.parameterValue?.value?.value?.id ? String(item?.parameterValue?.value?.value?.id) : ' '}
                label='Территория'
                onChange={(value) => {
                  changeValue('CONSUMPTION_SCHEDULE_BINDING', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case 'RESTRICTION_SO_MODE_WATCH':
        return <></>
      case 'PARTICIPATION_NPRCH':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={classNames(cls.LimitCell, { [cls.ExtraRowError]: errors.has('PARTICIPATION_NPRCH') })}
                label='Абсолютное значение, МВт'
                type='number'
                toFixed={3}
                positiveNumber
                value={item?.parameterValue?.value?.value ? String(item.parameterValue.value.value) : ''}
                onChange={(e) => {
                  const value = e.target.value
                  changeValue('PARTICIPATION_NPRCH', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case 'PARTICIPATION_AVRCHM':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.LimitCell}
                label='Абсолютное значение, МВт'
                type='number'
                toFixed={3}
                positiveNumber
                maxLength={5}
                value={item.parameterValue.value.value && String(item?.parameterValue?.value?.value)}
                onChange={(e) => {
                  const value = e.target.value
                  if (Number(value) >= 0) {
                    changeValue('PARTICIPATION_AVRCHM', value, '')
                    setEditMode(true)
                  }
                }}
              />
            )}
          </>
        )
      case 'PARTICIPATION_AVRCHM_HIGH_LOAD':
        return <></>
      case 'PRIORITY_LOAD_RGU':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <Select
                disabled={!isEditPlant || !editModeRole || isPastDate}
                variant='outlined'
                className={cls.Voltage}
                items={finVoltages}
                value={item.parameterValue.value.value ? String(item.parameterValue.value.value) : ''}
                label='Напряжение, кВ'
                onChange={(value) => {
                  changeValue('PRIORITY_LOAD_RGU', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      case 'GENERATOR_JOINT_WORK_WATCH':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <>
                {isEditPlant && editModeRole && !isPastDate && (
                  <Button
                    variant='text'
                    className={cls.AddButton}
                    onClick={() => {
                      setCreateGroup({})
                      errors.delete('GENERATOR_JOINT_WORK_WATCH')
                    }}
                  >
                    <>Добавить</>
                    <AddIcon width={16} />
                  </Button>
                )}
                <GroupsList
                  disabled={!isEditPlant || !editModeRole || isPastDate}
                  items={item?.parameterValue?.value?.value?.groups ?? []}
                  setDeleteGroup={setDeleteGroup}
                  setCreateGroup={setCreateGroup}
                  errors={errors}
                />
              </>
            )}
          </>
        )
      case 'MINIMUM_LIMIT':
        return (
          <div className={cls.OtherCell}>
            {item.parameterValue.value.turnedOn && (
              <>
                <TextField
                  disabled
                  className={cls.LimitCell}
                  label='МВт'
                  type='number'
                  toFixed={3}
                  positiveNumber
                  value={item?.parameterValue?.value?.value?.actualLimit}
                  onChange={(e) => {
                    changeValue('MINIMUM_LIMIT', e.target.value, '')
                    setEditMode(true)
                  }}
                  inputProps={{ readOnly: true }}
                />
                <Button
                  variant='text'
                  className={cls.ExtraButton}
                  onClick={() => {
                    setIsLimitModal({
                      mode: 'min',
                      ...selectItem,
                      restrictions: item?.parameterValue?.value?.value?.restrictions,
                    })
                    setErrors((prev) => {
                      prev.clear()

                      return prev
                    })
                  }}
                >
                  Список ограничений
                </Button>
              </>
            )}
          </div>
        )
      case 'MAXIMUM_LIMIT':
        return (
          <div className={cls.OtherCell}>
            {item.parameterValue.value.turnedOn && (
              <>
                <TextField
                  disabled
                  className={cls.LimitCell}
                  label='МВт'
                  type='number'
                  toFixed={3}
                  positiveNumber
                  value={item?.parameterValue?.value?.value?.actualLimit}
                  onChange={(e) => {
                    changeValue('MAXIMUM_LIMIT', e.target.value, '')
                    setEditMode(true)
                  }}
                  inputProps={{ readOnly: true }}
                />
                <Button
                  variant='text'
                  className={cls.ExtraButton}
                  onClick={() => {
                    setIsLimitModal({
                      mode: 'max',
                      ...selectItem,
                      restrictions: item?.parameterValue?.value?.value?.restrictions,
                    })
                    setErrors((prev) => {
                      prev.clear()

                      return prev
                    })
                  }}
                >
                  Список ограничений
                </Button>
              </>
            )}
          </div>
        )
      case 'PRESSURE_RECESSION_WATCH':
        return item.parameterValue.value.turnedOn ? (
          <div className={cls.FLOOD_CONTAINER}>
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Величина остаточной выработки, МВт*ч'
              type='number'
              value={
                item?.parameterValue?.value?.value?.residualOutput &&
                String(item?.parameterValue?.value?.value?.residualOutput)
              }
              className={cls.FloodCell}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'residualOutput')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              label='Коэффициент, определяющий долю потерь на снижение потерь на снижение напора в каждом ГГ, о.е'
              type='number'
              value={item?.parameterValue?.value?.value?.lossPortionRatio ?? ''}
              className={cls.FloodCell}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'lossPortionRatio')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              label='Мощность ГГ в генераторном режиме, МВт'
              type='number'
              value={item?.parameterValue?.value?.value?.generatorPower ?? ''}
              disabled={!isEditPlant || !editModeRole || isPastDate}
              className={cls.FloodCell}
              onChange={(e) => {
                const value = e.target.value
                if (Number(value) >= 0) {
                  changeValue('PRESSURE_RECESSION_WATCH', value, 'generatorPower')
                  setEditMode(true)
                }
              }}
            />
          </div>
        ) : (
          <></>
        )
      case 'LOAD_UNLOAD_SPEED':
        return item.parameterValue.value.turnedOn ? (
          <div className={classNames(cls.FLOOD_CONTAINER, { [cls.LOAD_UNLOAD_SPEED]: true }, [])}>
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Скорость набора нагрузки, МВт/час'
              type='number'
              value={item?.parameterValue?.value?.value?.loadSpeed}
              className={cls.EminMax}
              onChange={(e) => {
                const value = e.target.value.replace(/[^0-9.]+/g, '')
                if (value !== '') {
                  changeValue('LOAD_UNLOAD_SPEED', value === '' ? undefined : value, 'loadSpeed')
                  setEditMode(true)
                } else {
                  changeValue('LOAD_UNLOAD_SPEED', value, 'loadSpeed')
                  setEditMode(true)
                }
              }}
            />
            <TextField
              disabled={!isEditPlant || !editModeRole || isPastDate}
              label='Скорость снижения нагрузки, МВт/час'
              type='number'
              value={item?.parameterValue?.value?.value?.unloadSpeed}
              className={cls.EminMax}
              onChange={(e) => {
                const value = e.target.value.replace(/[^0-9.]+/g, '')
                if (value !== '') {
                  changeValue('LOAD_UNLOAD_SPEED', value === '' ? undefined : value, 'unloadSpeed')
                  setEditMode(true)
                } else {
                  changeValue('LOAD_UNLOAD_SPEED', value, 'unloadSpeed')
                  setEditMode(true)
                }
              }}
            />
          </div>
        ) : (
          <></>
        )
      case 'EFFICIENCY':
        return <></>
      case 'RGU_GROUP':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.LimitCell}
                label='Номер группы'
                type='number'
                positiveNumber
                value={item?.parameterValue?.value?.value && (item?.parameterValue?.value?.value as unknown as string)}
                onChange={(e) => {
                  const value = e.target.value
                  if (Number(value) >= 0) {
                    changeValue('RGU_GROUP', e.target.value, '')
                    setEditMode(true)
                  }
                }}
              />
            )}
          </>
        )
      case 'TERTIARY_RESERVE':
        return (
          <>
            {item.parameterValue.value.turnedOn && (
              <TextField
                disabled={!isEditPlant || !editModeRole || isPastDate}
                className={cls.EminMax}
                label='Абсолютное значение, млн. кВт*ч'
                type='number'
                toFixed={3}
                positiveNumber
                value={item?.parameterValue?.value?.value ? String(item.parameterValue.value.value) : ''}
                onChange={(e) => {
                  const value = e.target.value
                  changeValue('TERTIARY_RESERVE', value, '')
                  setEditMode(true)
                }}
              />
            )}
          </>
        )
      default:
        return <></>
    }
  }

  const validateLimits = (MIN: number | undefined, MAX: number | undefined) => {
    if (MIN === undefined && MAX === undefined) {
      const textError = `Введите ограничения`
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MIN === undefined) {
      const textError = `Введите ограничение`
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MAX === undefined) {
      const textError = `Введите ограничение`
      setErrors((prev) => {
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }
    if (MIN > MAX) {
      const textError = 'Ограничение минимума не может быть больше ограничения максимума'
      setErrors((prev) => {
        prev.set('MINIMUM_LIMIT', textError)
        prev.set('MAXIMUM_LIMIT', textError)

        return prev
      })

      return false
    }

    return true
  }

  const ggAdds = prepareFlatData(configs).map((el) => ({
    ...el,
    type: el?.generators ? 'GEN' : 'STATION',
  }))
  const groupsArr =
    paramsFinal
      .find((el) => el.parameterName === 'GENERATOR_JOINT_WORK_WATCH')
      ?.parameterValue?.value?.value?.groups.map((el) => {
        const children = el?.childs?.length > 0 ? el?.childs : []

        return { ...el, children }
      }) ?? []
  const groups = groupsArr?.length > 0 ? prepareFlatData(groupsArr).filter((el) => el.id) : []

  const ggsAdd = ggAdds
    .filter((el) => el.type === 'STATION')
    .filter((el) => {
      return !groups.some((item) => (item.id as unknown as number) === el.id)
    })

  const isEFFICIENCY = params.some((el) => el.parameterName === 'EFFICIENCY')

  const reinitDataAfterSave = (plantId: number, prepareDate: string) => {
    init()
    getParams(plantId, prepareDate)
    setEditMode(false)
    setErrors((prev) => {
      prev.clear()

      return prev
    })
  }

  const onSave = async () => {
    const optim = params.find((el) => el.parameterName === 'OPTIMIZATION')
    const adj = params.find((el) => el.parameterName === 'REGULATED_UNIT')
    const eff = params.find((el) => el.parameterName === 'EFFICIENCY')

    const isOptim =
      optim?.parameterValue?.value?.turnedOn !== undefined &&
      String(optim?.parameterValue?.value?.turnedOn) !== String(optimization)
    const isAdj =
      adj?.parameterValue?.value !== undefined && (adj?.parameterValue?.value as unknown as string) !== adjustableUnit
    const isEff = isEFFICIENCY && eff && (eff.parameterValue.value as unknown as string) !== efficiency

    const parameters = [
      ...paramsFinal
        .filter((el) => el?.isEdit)
        .map((el) => {
          if (el.parameterName === 'PRESSURE_RECESSION_WATCH') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: {
                    ...el.parameterValue.value.value,
                    residualOutput:
                      el.parameterValue.value.value?.residualOutput === undefined ||
                      el.parameterValue.value.value?.residualOutput === '' ||
                      el.parameterValue.value.value?.residualOutput === '0'
                        ? null
                        : Number(el.parameterValue.value.value.residualOutput),
                    lossPortionRatio:
                      el.parameterValue.value.value?.lossPortionRatio === undefined ||
                      el.parameterValue.value.value.lossPortionRatio === '' ||
                      el.parameterValue.value.value.lossPortionRatio === '0'
                        ? null
                        : Number(el.parameterValue.value.value.lossPortionRatio),
                    generatorPower:
                      el.parameterValue.value.value?.generatorPower === undefined ||
                      el.parameterValue.value.value.generatorPower === '' ||
                      el.parameterValue.value.value.generatorPower === '0'
                        ? null
                        : Number(el.parameterValue.value.value.generatorPower),
                  },
                },
              },
            }
          }
          if (el.parameterName === 'LOAD_UNLOAD_SPEED') {
            const loadSpeed = el.parameterValue.value.value?.loadSpeed
              ? String(el.parameterValue.value.value?.loadSpeed)
              : ''
            const unloadSpeed = el.parameterValue.value.value?.unloadSpeed
              ? String(el.parameterValue.value.value?.unloadSpeed)
              : ''

            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: {
                    ...el.parameterValue.value.value,
                    loadSpeed,
                    unloadSpeed,
                  },
                },
              },
            }
          }
          if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value: el.parameterValue.value.value.groups.length === 0 ? null : el.parameterValue.value.value,
                },
              },
            }
          }
          if (['PARTICIPATION_AVRCHM', 'PARTICIPATION_NPRCH'].includes(el.parameterName)) {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value:
                    (el.parameterValue.value.value as unknown as string) === '' ||
                    (el.parameterValue.value.value as unknown as string) === '0'
                      ? null
                      : el.parameterValue.value.value,
                },
              },
            }
          }
          if (el.parameterName === 'E_MAX_E_MIN') {
            return {
              parameterName: el.parameterName,
              parameterValue: {
                ...el.parameterValue,
                value: {
                  ...el.parameterValue.value,
                  value:
                    (el.parameterValue.value.value as unknown as string) === '' ||
                    (el.parameterValue.value.value as unknown as string) === '0'
                      ? null
                      : parseFloat(el.parameterValue.value.value as unknown as string) * 1000,
                },
              },
            }
          }

          return {
            parameterName: el.parameterName,
            parameterValue: el.parameterValue,
          }
        }),
    ]
    if (isOptim) {
      const optimObject = {
        ...optim,
        parameterValue: {
          ...optim.parameterValue,
          value: {
            turnedOn: JSON.parse(optimization as string),
          },
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(optimObject)
    }
    if (isAdj) {
      const adjObject = {
        ...adj,
        parameterValue: {
          ...adj.parameterValue,
          value: adjustableUnit,
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(adjObject)
    }
    if (isEff) {
      const effObject = {
        ...eff,
        parameterValue: {
          ...eff?.parameterValue,
          value: Number(efficiency),
        },
      } as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>
      parameters.push(effObject)
    }

    const generatorsRows = (arr: IPrepareRgu[]) => {
      const newRows = []

      for (const iterator of arr) {
        if (iterator.children) {
          newRows.push(...iterator.children)
        }
      }

      return newRows
    }

    const res = {
      id: selectLeftMenu,
      parameters,
      rgus: [
        ...rows
          .filter((el) => el?.isEdit)
          .map((el) => {
            const parameters = el?.parameters?.map((par) => {
              if (
                par.parameterName === 'VOLTAGE' &&
                (par.parameterValue.value as unknown as number) !== Number(el.voltage)
              ) {
                if (el.voltage || el.voltage === '') {
                  return {
                    ...par,
                    parameterValue: {
                      ...par.parameterValue,
                      value: Number(el.voltage) || null,
                      edited: true,
                    },
                  }
                } else {
                  const res = par.parameterValue ? par.parameterValue : { parameterValue: null }

                  return res
                }
              }
              if (par.parameterName === 'PARTICIPATION_AVRCHM' && par.parameterValue.value.turnedOn !== el.avrchm) {
                return {
                  ...par,
                  parameterValue: {
                    ...par.parameterValue,
                    value: {
                      ...par.parameterValue.value,
                      turnedOn: el.avrchm,
                    },
                    edited: true,
                  },
                }
              }
              if (par.parameterName === 'PRIORITY_LOAD' && par.parameterValue.value.turnedOn !== el.priorityLoad) {
                return {
                  ...par,
                  parameterValue: {
                    ...par.parameterValue,
                    value: {
                      turnedOn: el.priorityLoad,
                    },
                    edited: true,
                  },
                }
              }

              return par
            })
            const temp = parameters as unknown as {
              parameterValue: {
                edited: boolean
                type: string
                value: string
              }
            }[]
            const filteredParameters = temp
              ? temp
                  .filter((item) => item?.parameterValue?.edited)
                  .map((item) => ({
                    ...item,
                    parameterValue: {
                      type: item?.parameterValue?.type,
                      value: item?.parameterValue?.value,
                    },
                  }))
              : []

            const startDate = el.startDate ? el.startDate.split('.').reverse().join('-') : null
            const endDate = el.endDate ? el.endDate.split('.').reverse().join('-') : null

            return {
              id: el.id,
              parameters: filteredParameters,
              startDate,
              endDate,
            }
          }),
      ],
      generators: [
        ...generatorsRows(rows)
          .filter((el) => el.isEdit)
          .map((el) => {
            const parameters = el.parameters
              ? el.parameters.map(
                  (par: {
                    parameterName: string
                    parameterValue: {
                      value: { turnedOn: boolean }
                    }
                  }) => {
                    if (
                      par.parameterName === 'PARTICIPATION_AVRCHM' &&
                      par.parameterValue.value.turnedOn !== el.avrchm
                    ) {
                      return {
                        ...par,
                        parameterValue: {
                          ...par.parameterValue,
                          value: {
                            ...par.parameterValue.value,
                            turnedOn: el.avrchm,
                          },
                        },
                      }
                    }
                    if (
                      par.parameterName === 'PRIORITY_LOAD' &&
                      par.parameterValue.value.turnedOn !== el.priorityLoad
                    ) {
                      return {
                        ...par,
                        parameterValue: {
                          ...par.parameterValue,
                          value: {
                            turnedOn: el.priorityLoad,
                          },
                        },
                      }
                    }

                    return par
                  },
                )
              : []
            const startDate = el.startDate ? el.startDate.split('.').reverse().join('-') : null
            const endDate = el.endDate ? el.endDate.split('.').reverse().join('-') : null

            return {
              id: el.id,
              parameters,
              startDate,
              endDate,
            }
          }),
      ],
    }

    const year = date.getFullYear()
    const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
    const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`
    const prepareDate = `${year}-${month}-${day}`

    const err = validate(parameters, optimization as unknown as string)

    setErrors(err)
    if (err.size) {
      return
    }
    const INIT_MIN = paramsFinal?.find((el) => el?.parameterName === 'MINIMUM_LIMIT')?.parameterValue?.value?.value
      ?.actualLimit
    const INIT_MAX = paramsFinal?.find((el) => el?.parameterName === 'MAXIMUM_LIMIT')?.parameterValue?.value?.value
      ?.actualLimit

    const checkLimit = (limit: unknown) => {
      const limitType = typeof limit
      if ((limitType === 'number' || limitType === 'string') && !Number.isNaN(Number(limit))) return Number(limit)

      return undefined
    }

    const LIMIT_MIN = checkLimit(INIT_MIN)
    const LIMIT_MAX = checkLimit(INIT_MAX)
    const TURN_ON_MIN =
      paramsFinal?.find((el) => el?.parameterName === 'MINIMUM_LIMIT')?.parameterValue?.value?.turnedOn ?? false
    const TURN_ON_MAX =
      paramsFinal?.find((el) => el?.parameterName === 'MAXIMUM_LIMIT')?.parameterValue?.value?.turnedOn ?? false
    const isValidate = TURN_ON_MIN && TURN_ON_MAX ? validateLimits(LIMIT_MIN, LIMIT_MAX) : true

    if (isValidate) {
      await saveParams(res as unknown as ISaveParamsInput, prepareDate).then(() => {
        if (selectItem?.plantId) {
          reinitDataAfterSave(selectItem?.plantId, prepareDate)
        }
      })
    }
  }

  const onReset = () => {
    setEditMode(false)
    setRows(initialRows)
    if (params.length > 0) {
      const curAdjustableUnit = params.find((el) => el.parameterName === 'REGULATED_UNIT')
      if (curAdjustableUnit) {
        setAdjustableUnit(curAdjustableUnit?.parameterValue?.value as unknown as string)
      }
      const curOptimization = params.find((el) => el.parameterName === 'OPTIMIZATION')
      if (curOptimization) {
        setOptimization(curOptimization.parameterValue.value.turnedOn)
      }
      const curEfficiency = params.find((el) => el.parameterName === 'EFFICIENCY')
      if (curEfficiency) {
        setEfficiency(curEfficiency.parameterValue.value as unknown as string)
      }
      const res = params
        .filter(
          (el) =>
            el.parameterName !== 'REGULATED_UNIT' &&
            el.parameterName !== 'OPTIMIZATION' &&
            el.parameterName !== 'EFFICIENCY',
        )
        .sort((a, b) => a.priority - b.priority)
      setParamsFinal(res as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[])
    }

    setErrors((prev) => {
      prev.clear()

      return prev
    })
  }

  const isGes = useMemo(() => {
    const selectStation = finalPlants.find((item) => item.value === selectLeftMenu)
    if (!selectStation) return false

    return /ГЭС/.test(selectStation.label)
  }, [finalPlants, selectLeftMenu])

  useHotkeys('ctrl+shift+s', () => (editMode || isEditRows) && onSave(), { enableOnFormTags: true })
  useHotkeys('ctrl+shift+x', () => (editMode || isEditRows) && onReset(), { enableOnFormTags: true })

  const bodyRef = useRef<HTMLDivElement | null>(null)
  const [height, setHeight] = useState<number | null>(null)

  const changeHeightTable = () => {
    if (bodyRef.current) {
      const padding = getComputedStyle(bodyRef.current, null).getPropertyValue('padding')
      const curHeight = bodyRef.current.getBoundingClientRect().height - 2 * parseInt(padding)
      setHeight(curHeight)
    }
  }

  const setRef = (ref: HTMLDivElement | null) => {
    bodyRef.current = ref
    changeHeightTable()
  }

  useEffect(() => {
    window.addEventListener('resize', changeHeightTable)

    return () => {
      window.removeEventListener('resize', changeHeightTable)
    }
  }, [])

  const tabs: Record<TabKey, React.ReactNode> = {
    main: (
      <>
        <div>
          {isGes && (
            <div className={cls.Row}>
              <div className={cls.Cell}>Регулируемая единица :</div>
              <div className={cls.Cell}>
                <Button
                  variant='text'
                  className={cls.ButtonHistoryForRegular}
                  onClick={() => {
                    const item = params.find((el) => el.parameterName === 'REGULATED_UNIT')
                    item && setIsHistoryModal(item as IHistoryModal)
                  }}
                >
                  <Icon width={14} name='history' />
                </Button>
                <Select
                  variant='outlined'
                  disabled={!isEditPlant || !editModeRole || isPastDate}
                  value={adjustableUnit}
                  items={adjustableUnitList}
                  onChange={(value) => {
                    setAdjustableUnit(value)
                    setEditMode(true)
                  }}
                />
              </div>
            </div>
          )}
          {isGes && (
            <div className={cls.Row}>
              <div className={cls.Cell}>Оптимизация :</div>
              <div className={cls.Cell}>
                <Button
                  variant='text'
                  className={cls.ButtonHistoryForRegular}
                  onClick={() => {
                    const item = params.find((el) => el.parameterName === 'OPTIMIZATION')
                    item && setIsHistoryModal(item)
                  }}
                >
                  <Icon width={14} name='history' />
                </Button>
                <Select
                  variant='outlined'
                  disabled={!isEditPlant || !editModeRole || isPastDate}
                  value={optimization as unknown as string}
                  items={optimizationList}
                  onChange={(value) => {
                    setOptimization(value)
                    setEditMode(true)
                  }}
                />
              </div>
            </div>
          )}
          {isEFFICIENCY && (
            <div className={cls.Row}>
              <div className={cls.Cell}>КПД ГАЭС :</div>
              <div className={cls.Cell}>
                <Button
                  variant='text'
                  className={cls.ButtonHistoryForRegular}
                  onClick={() => {
                    const item = params.find((el) => el.parameterName === 'EFFICIENCY')
                    item && setIsHistoryModal(item)
                  }}
                >
                  <Icon width={14} name='history' />
                </Button>
                <TextField
                  value={efficiency}
                  disabled={!isEditPlant || !editModeRole || isPastDate}
                  className={cls.Efficiency}
                  variant='standard'
                  onChange={(e) => {
                    const value = e.target.value
                    if (Number(value) >= 1 && Number(value) <= 2 && String(e.target.value).length <= 5) {
                      setEfficiency(value)
                      setEditMode(true)
                    }
                  }}
                />
              </div>
            </div>
          )}
        </div>
        <div ref={setRef} className={cls.Table}>
          <Table
            loading={isLoadingTable}
            childKey='name'
            height={height !== null ? height - 10 : 680}
            initialData={initialRows}
            rows={rows}
            columns={columns}
            setRows={setRows}
            editMode={(isEditPlant && editModeRole) || isPastDate}
            expandedRowIds={expandedRowIds}
            setExpandenRowIds={setExpandedRowIds}
            hiddenColumnNames={hiddenColumns}
            columnSearchDisabled={['avrchm', 'priorityLoad']}
            ROW_HEIGHT={27} // Фиксируем высоту строк, что бы они не "прыгали" при изменении состояния disabled
          />
        </div>
      </>
    ),
    extra: (
      <div className={cls.Extra}>
        {paramsFinal.map((el) => {
          const turnedOn = el?.parameterValue?.value?.turnedOn ?? false
          const isGenerator = el.parameterName === 'GENERATOR_JOINT_WORK_WATCH'
          const isPressure = el.parameterName === 'PRESSURE_RECESSION_WATCH' || el.parameterName === 'LOAD_UNLOAD_SPEED'

          return (
            <div
              key={`${el.plantId}_${el.parameterName}`}
              className={classNames(
                cls.ExtraRow,
                {
                  [cls.Pressure]: isPressure,
                  [cls.ExtraRowError]: errors.has(el.parameterName),
                },
                [],
              )}
            >
              {errors.has(el.parameterName) && (
                <Tooltip title={errors.get(el.parameterName)} className={cls.infoIcon}>
                  <div>
                    <Icon name='information' width={20} />
                  </div>
                </Tooltip>
              )}
              <IconButton
                onClick={() => {
                  setIsHistoryModal(el)
                }}
                className={classNames(
                  cls.ButtonInfo,
                  {
                    [cls.isPressureInformation]: isPressure,
                  },
                  [],
                )}
              >
                <Icon width={14} name='history' />
              </IconButton>
              <div
                title={
                  el.parameterName === 'E_MAX_E_MIN' && !optimization
                    ? 'Дельта Эмакс - Эмин может быть включена только для оптимизируемых ГЭС'
                    : ''
                }
              >
                <Switch
                  disabled={
                    !isEditPlant || isPastDate || !editModeRole || (el.parameterName === 'E_MAX_E_MIN' && !optimization)
                  }
                  onChange={(_: unknown, value) => {
                    setParamsFinal((prev) => {
                      return prev.map((item) => {
                        if (item.parameterName === el.parameterName) {
                          const { parameterValue } = item
                          if (el.parameterName === 'EFFICIENCY') {
                            return {
                              ...item,
                              parameterValue: {
                                ...parameterValue,
                                value: { ...parameterValue.value, turnedOn: value, value: '1' },
                              },
                              isEdit: true,
                            }
                          }

                          return {
                            ...item,
                            parameterValue: {
                              ...parameterValue,
                              value: { ...parameterValue.value, turnedOn: value },
                            },
                            isEdit: true,
                          }
                        }

                        return item
                      }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
                    })
                    setEditMode(true)
                    errors.delete(el.parameterName)
                  }}
                  checked={turnedOn}
                />
              </div>
              <div
                className={classNames(cls.ButtonInfo, {
                  [cls.GroupsExtra]: isGenerator,
                  [cls.Pressure]: isPressure,
                })}
              >
                {el.description}
              </div>
              <div
                className={classNames(
                  `${cls.ExtraCell} ${el.parameterName === 'GENERATOR_JOINT_WORK_WATCH' ? cls.ExtraCellGroup : ''}`,
                  { [cls.ExtraPressure]: isPressure },
                )}
              >
                {getExtraContent(el.parameterName, el)}
              </div>
            </div>
          )
        })}
      </div>
    ),
    displaySettings: <DisplaySettings selectedPlant={selectItem} />,
  }

  return (
    <div className={cls.ListOfStations}>
      <div className={cls.Right}>
        {isLoading ? (
          <div className={cls.LoaderContainer}>
            <Loader />
          </div>
        ) : (
          <>
            {selectItem ? (
              <div className={cls.DataContainer}>
                <div className={cls.HeaderRight}>
                  <div className={cls.HeaderTitle}>{selectItem.label}</div>
                  <div>
                    <Toggle
                      items={characteristicsList}
                      value={characteristics}
                      setValue={(e) => {
                        setIsLoadingTable(true)
                        setCharacteristics(e)
                        setTimeout(() => {
                          setIsLoadingTable(false)
                        }, TIME_LOADER)
                      }}
                    />
                  </div>
                  <div className={cls.Actions}>
                    <Button disabled={!(editMode || isEditRows)} variant='outlined' onClick={onReset}>
                      Сбросить
                    </Button>
                    <Button disabled={!(editMode || isEditRows)} onClick={onSave}>
                      Сохранить
                    </Button>
                  </div>
                </div>
                {tabs[characteristics]}
              </div>
            ) : (
              <div className={cls.NoData}>Выберите станцию</div>
            )}
          </>
        )}
      </div>
      {deleteModal && (
        <DeleteModal
          onClose={() => {
            setDeleteModal(false)
          }}
          item={deleteItem as unknown as { label: string }}
          onConfirm={async () => {
            await trashDepPlants(deleteItem?.value as unknown as number).then(() => {
              setDeleteModal(false)
            })
          }}
        />
      )}
      {deleteGroup && (
        <DeleteModal
          onClose={() => {
            setDeleteGroup(null)
          }}
          item={deleteGroup as unknown as { label: string }}
          onConfirm={() => {
            setParamsFinal((prev) => {
              return prev.map((el: IParametersConfigsOutput<ValueConfigsOutputWithGroups>) => {
                if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: {
                          ...el.parameterValue.value.value,
                          groups: el.parameterValue.value.value.groups.filter(
                            (item) => item.value !== deleteGroup.value,
                          ),
                        },
                      },
                    },
                  }
                }

                return el
              })
            })
            setDeleteGroup(null)
            setEditMode(true)
          }}
        />
      )}
      {isLimitModal && (
        <LimitModal
          object={isLimitModal}
          disabled={!isEditPlant || !editModeRole || isPastDate}
          onConfirm={(res) => {
            const type = isLimitModal.mode === 'min' ? 'MINIMUM_LIMIT' : 'MAXIMUM_LIMIT'
            const actualLimitTemp = res.filter((el) => el.active).map((el) => Number(el.limit))
            const getLimit = () => {
              if (type === 'MINIMUM_LIMIT') {
                return Math.max.apply(null, actualLimitTemp).toFixed(3)
              }
              if (type === 'MAXIMUM_LIMIT') {
                return Math.min.apply(null, actualLimitTemp).toFixed(3)
              }
            }
            const actualLimit = actualLimitTemp.length > 0 ? getLimit() : '0'
            setParamsFinal((prev) => {
              return prev.map((el) => {
                if (el.parameterName === type) {
                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: {
                          ...el.parameterValue.value.value,
                          actualLimit,
                          restrictions: res,
                        },
                      },
                    },
                  }
                }

                return el
              }) as unknown as IParametersConfigsOutput<ValueConfigsOutputWithGroups>[]
            })
            setIsLimitModal(null)
            setEditMode(true)
          }}
          onClose={() => {
            setIsLimitModal(null)
          }}
        />
      )}
      {isHistoryModal && (
        <HistoryParamsModal
          subtitle={selectItem?.label ?? ''}
          object={
            isHistoryModal as unknown as {
              id: number & string
              plantId: number
              type: string
              parameterName: string & number
              name: string
              generatorName: string
            }
          }
          onClose={() => {
            setIsHistoryModal(null)
          }}
        />
      )}
      {createGroup && (
        <AddGroupsModal
          object={createGroup as unknown as IObj}
          setObject={setCreateGroup}
          onClose={() => {
            setCreateGroup(null)
          }}
          onConfirm={(res: { type: string; value: string }) => {
            setParamsFinal((prev) => {
              return prev.map((el) => {
                if (el.parameterName === 'GENERATOR_JOINT_WORK_WATCH') {
                  const groups = [...el.parameterValue.value.value.groups]
                  if (res.type === 'edit') {
                    const index = groups.findIndex((item) => item.value === res.value)
                    groups.splice(index, 1, res as unknown as ExValueConfigsOutput)
                  } else groups.push(res as unknown as ExValueConfigsOutput)

                  return {
                    ...el,
                    isEdit: true,
                    parameterValue: {
                      ...el.parameterValue,
                      value: {
                        ...el.parameterValue.value,
                        value: { ...el.parameterValue.value.value, groups: groups },
                      },
                    },
                  }
                }

                return el
              })
            })
            setCreateGroup(null)
            setEditMode(true)
          }}
          ggs={ggsAdd as unknown as IObj[]}
        />
      )}
      <WarningsModal reinitDataAfterSave={reinitDataAfterSave} />
    </div>
  )
})
