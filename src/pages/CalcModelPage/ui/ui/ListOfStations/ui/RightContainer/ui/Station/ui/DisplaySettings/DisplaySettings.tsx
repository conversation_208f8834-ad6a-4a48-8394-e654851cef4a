import './DisplaySettings.module.scss'

import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { IPlantForLeftMenu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'

type GridSettings = Handsontable.GridSettings

interface DisplaySettingsProps {
  selectedPlant: IPlantForLeftMenu | null | undefined
}

export const DisplaySettings = observer(({ selectedPlant }: DisplaySettingsProps) => {
  const { calcModelStore } = useStore()
  const {
    listOfStationsStore: { displaySettingsStore },
  } = calcModelStore
  const { formattedDate } = calcModelStore
  const { getDisplaySettings, displaySettingsSpreadsheetData } = displaySettingsStore
  const { columns, data, nestedHeaders, cell, rowHeaders } = displaySettingsSpreadsheetData

  /**
   * Хук afterGetColHeader
   *
   * Функция-обработчик, стилизующая заголовки столбцов.
   * Если индекс столбца равен -1, используется для отображения наименования формы.
   *
   * @param col - индекс столбца (если равен -1, это наименование формы).
   * @param TH - HTML-элемент, в который выводится заголовок столбца.
   */
  const afterGetColHeader: GridSettings['afterGetColHeader'] = (col, TH, level) => {
    const isFormNameColumn = col === -1 && level === 0
    if (isFormNameColumn) {
      TH.textContent = 'Наименование формы'
    }

    TH.style.fontWeight = 'bold'
  }

  useEffect(() => {
    getDisplaySettings(selectedPlant?.plantId, formattedDate)
  }, [selectedPlant, formattedDate])

  return (
    <>
      <SpreadsheetReact
        data={data}
        columns={columns}
        nestedHeaders={nestedHeaders}
        cell={cell}
        rowHeaders={rowHeaders}
        rowHeaderWidth={140}
        afterGetColHeader={afterGetColHeader}
      />
    </>
  )
})
