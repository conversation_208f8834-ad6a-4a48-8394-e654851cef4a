import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse'
import { makeAutoObservable, runInAction } from 'mobx'
import { RootStore } from 'stores/RootStore'
import { convertSpreadsheetResponseToComponentProps } from 'widgets/Spreadsheet/ui/lib'

import { getDisplaySettings } from '../api'
import { IDisplaySettingsStore } from './DisplaySettingsStore.types'

const emptySpreadsheetStructure = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class DisplaySettingsStore implements IDisplaySettingsStore {
  rootStore: IDisplaySettingsStore['rootStore']
  displaySettingsSpreadsheetData: IDisplaySettingsStore['displaySettings'] = { ...emptySpreadsheetStructure }
  isDisplaySettingsLoading: IDisplaySettingsStore['isDisplaySettingsLoading'] = false

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  getDisplaySettings = async (plantId: number, date: string) => {
    try {
      this.isDisplaySettingsLoading = true
      // 1) Сразу получаем «сырой» ответ
      const res = await getDisplaySettings(plantId, date)

      // 2) «Оборачиваем» наш ответ в тот же интерфейс IGetSpreadsheetData,
      //    но вставляем в каждую cell.value = cell.visible
      const wrappedPayload: IGetSpreadsheetData<{ visible: boolean }> = {
        columns: res.columns,
        rows: res.rows.map((row) => ({
          title: row.title,
          cells: row.cells.map((cell) => ({
            // сюда конвертер заглянет и положит в data[][] именно visible
            value: cell.visible,
            // а остальное (column, section, accepted и т.д.) пойдет в метаданные cell
            ...cell,
          })),
        })),
      }

      // 3) Прокидываем в конвертер «обёрнутый» ответ
      const converted = convertSpreadsheetResponseToComponentProps(wrappedPayload)

      // 4) Проставляем чекбоксовые колонки и редакторы
      const columns = converted.columns.map(() => ({
        type: 'checkbox',
        // editor: 'checkbox',
        // renderer: 'checkbox',
        className: 'htCenter',
      }))

      const cell = converted.cell.map((c) => ({
        ...c,
        // cell.value уже boolean
        // editor: 'checkbox',
        // renderer: 'checkbox',
      }))

      runInAction(() => {
        this.displaySettingsSpreadsheetData = {
          ...converted,
          columns,
          cell,
        }
      })
    } catch (e) {
      console.error(e)
    } finally {
      runInAction(() => {
        this.isDisplaySettingsLoading = false
      })
    }
  }
}
