.wrapper {
  height: 100%;
}

.header {
  height: var(--header-page);
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-page);
  padding: 0 16px;
}

.body {
  border-radius: 8px;
  background-color: var(--background-color-secondary);
  margin-top: 0.2rem;
  // высота элемента - 100% - высота заголовка вкладки и - отступ между ними
  height: calc(100% - var(--header-page) - 0.2rem);
  overflow: hidden;
  width: 100%;
}
