import { observer } from 'mobx-react'
import { useNavigate } from 'react-router-dom'
import { locationParse } from 'shared/lib/locationParse'
import { ItemsProps, Tabs } from 'widgets/Tabs/Tabs.tsx'

import { CalculationJournal } from './ui'
import cls from './WerCalculationPage.module.scss'

const tabs: ItemsProps[] = [
  {
    key: 'calculationjournal',
    label: 'Журнал расчетов',
    icon: 'book',
    isView: true,
  },
]

const WerCalculationPage = observer(() => {
  const history = useNavigate()
  const { path = 'calculationjournal' } = locationParse(location.search)

  return (
    <div className={cls.wrapper}>
      <div className={cls.header}>
        <Tabs
          items={tabs}
          selectedValue={path}
          onChange={(value) => {
            history(`?path=${value}`)
          }}
        />
      </div>
      <div className={cls.body}>{path === 'calculationjournal' && <CalculationJournal />}</div>
    </div>
  )
})

export default WerCalculationPage
