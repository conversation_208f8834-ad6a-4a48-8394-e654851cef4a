import { observer } from 'mobx-react'
import { useLayoutEffect, useMemo, useState } from 'react'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { useStore } from 'stores/useStore.ts'
import { TableV1 } from 'widgets/TableV1'

import cls from './CalculationJournal.module.scss'
import { calculationJournalColumns } from './lib'
import { ModalCreateEditCalculation } from './ui'

export const CalculationJournal = observer(() => {
  const { authStore, calcModelWerStore } = useStore()
  const { userDetail } = authStore
  const { calculationJournal } = calcModelWerStore
  const {
    active,
    canChangeRowPosition,
    objectiveFilterItems,
    departmentsFilterItems,
    calculationParams,
    initData,
    updateDateRange,
    fetchDepartmentsFilterItems,
    fetchCalculations,
    createCalculation,
    updateCalculation,
    deleteCalculation,
    increaseSerial,
    decreaseSerial,
    handleSorting,
    handleSearching,
    handleFiltering,
    setCurrentRowInViewport,
    setCacheControlRef,
    resetStore,
  } = calculationJournal
  const [loadingRequestsStatus, setLoadingRequestsStatus] = useState(true)
  const [loadingInitialDataStatus, setLoadingInitialDataStatus] = useState(true)

  useLayoutEffect(() => {
    fetchDepartmentsFilterItems().then(() => {
      setLoadingRequestsStatus(false)
    })

    return resetStore
  }, [])

  useLayoutEffect(() => {
    if (typeof userDetail.departmentId === 'number') {
      initData()
      setLoadingInitialDataStatus(false)
    }
  }, [userDetail.departmentId])

  const columns = useMemo(
    () =>
      calculationJournalColumns(
        createCalculation,
        decreaseSerial,
        increaseSerial,
        updateCalculation,
        deleteCalculation,
        objectiveFilterItems,
        departmentsFilterItems,
        canChangeRowPosition,
        userDetail,
      ),
    [objectiveFilterItems, departmentsFilterItems, canChangeRowPosition],
  )

  return (
    <div className={cls.wrapper}>
      <div className={cls.dateRange}>
        <DateRangePicker
          dateFrom={calculationParams.fromDate ? new Date(calculationParams.fromDate) : null}
          dateTo={calculationParams.toDate ? new Date(calculationParams.toDate) : null}
          handleChangeDate={updateDateRange}
        />
      </div>
      <div id='journal_table' className={cls.tableWrapper}>
        {!loadingInitialDataStatus && (
          <TableV1
            loading={loadingRequestsStatus}
            columns={columns}
            getRowId={(row) => row.id}
            asyncFetchParams={{
              onFetchRows: fetchCalculations,
              pageSize: calculationParams.limit,
              cacheControlRef: setCacheControlRef,
            }}
            searchNumberEditorColumns={['serialNumber']}
            onTopRowChange={setCurrentRowInViewport}
            onSortingChange={handleSorting}
            onSearchingChange={handleSearching}
            onFilteringChange={handleFiltering}
            rowHeight={25}
          />
        )}
        {active && <ModalCreateEditCalculation />}
      </div>
    </div>
  )
})
