import { FilteringStateProps, SortingStateProps } from '@devexpress/dx-react-grid'
import {
  IDepartmentDto,
  IWerCalculationDto,
  IWerCalculationElementDto,
  IWerCalculationIntervalDto,
  IWerCalculationParams,
} from 'pages/WerCalculationPage/ui/CalculationJournal/api/calculationJournalManager.ts'
import { ItemsProps } from 'shared/ui/Select/Select.tsx'
import { RootStore } from 'stores/RootStore.ts'
import { TableV1Props } from 'widgets/TableV1'
import { VirtualTableCacheControlRef } from 'widgets/TableV1/lib/useTableFetchRows.ts'
import { TFilterControlRadioSelectFiltering, TFilterControlTreeSelectFiltering } from 'widgets/TableV1/ui'

interface IWerCalculationRecord extends IWerCalculationDto {
  tabId: number
}

type EnrichItems<T> = T & ItemsProps

export interface ICalculationFormData {
  title: string
  department: number | null
  plantsCascades: number[]
  startDate: string
  endDate: string
  overallPeriod: boolean
  overallStartDate: string
  overallEndDate: string
  description: string
  objective: boolean
  objectivePlants: number[]
  calculationInterval: string
}

export interface IWerCalculationJournalStore {
  rootStore: RootStore
  calculations: IWerCalculationRecord[]
  totalRows: number
  cascadesPlants: EnrichItems<IWerCalculationElementDto>[]
  intervals: EnrichItems<IWerCalculationIntervalDto>[]
  objectiveFilterItems: TFilterControlRadioSelectFiltering['items']
  departmentsFilterItems: TFilterControlTreeSelectFiltering['items']
  rowDepartments: IDepartmentDto[]
  objectivePlants: EnrichItems<IWerCalculationElementDto>[]
  selectedPlant: IWerCalculationRecord | null
  active: boolean
  isLoading: boolean
  currentAbortController: AbortController | null
  calculationParams: IWerCalculationParams
  formData: ICalculationFormData
  formErrors: Record<keyof ICalculationFormData, string[]>
  currentRowInViewport: { topRow: number; bottomRow: number }
  cacheControlRef: VirtualTableCacheControlRef | null

  handleSorting: (sorting?: SortingStateProps['defaultSorting']) => Promise<void>
  handleSearching: (searching?: FilteringStateProps['defaultFilters']) => Promise<void>
  handleFiltering: Exclude<TableV1Props<IWerCalculationRecord>['onFilteringChange'], undefined>
}
