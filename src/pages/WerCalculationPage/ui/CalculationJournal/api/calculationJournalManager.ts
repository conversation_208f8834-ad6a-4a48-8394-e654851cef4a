import { axiosInstance as api } from 'shared/lib/axios'

export interface IWerCalculationIntervalDto {
  code: string
  title: string
}

export interface IWerCalculationElementDto {
  id: number
  type: 'DEPARTMENT' | 'PLANT' | 'CASCADE'
  name: string
  objective?: boolean
}

export interface IWerCalculationDto {
  id: number
  department: IWerCalculationElementDto
  serialNumber: number
  title: string
  plantsCascades: IWerCalculationElementDto[]
  startDate: string
  endDate: string
  overallPeriod: boolean
  overallStartDate: string
  overallEndDate: string
  description: string
  objective: boolean
  objectivePlants: IWerCalculationElementDto[]
  calculationInterval: {
    code: string
    title: string
  }
  changedDatetime: string
}

export interface IWerCalculationResponse {
  calculations: IWerCalculationDto[]
  totalCount: number
}

export interface IWerCalculationParams {
  fromDate?: string
  toDate?: string
  serialNumber?: number
  changedDateTime?: string
  title?: string
  plantOrCascadeName?: string
  startDate?: string
  endDate?: string
  description?: string
  objective?: boolean
  objectivePlantName?: string
  departmentIds?: string[]
  departmentName?: string
  sort?: `${string},${'ASC' | 'DESC'}`[]
  offset: number
  limit: number
}

export interface IDepartmentDto {
  active: true
  children: IDepartmentDto[]
  departmentLevel: 'CDU' | 'ODU' | 'RDU'
  id: number
  name: string
  startDate: string
  type: 'DEPARTMENT'
  uid: string
}

// Получение списка расчётов ВЭР
export const getWerCalculations = (
  payload: IWerCalculationParams,
  signal?: AbortSignal,
): Promise<IWerCalculationResponse> =>
  api.get('/api/v1/wer-calculation', {
    signal,
    params: payload,
    paramsSerializer: {
      indexes: null,
    },
  })

// Получение списка расчёта ВЭР по идентификатору
export const getWerCalculation = (id: number): Promise<IWerCalculationDto> => api.get(`/api/v1/wer-calculation/${id}`)

// Получение списка каскадов/станций, доступных для выбора в расчётах ВЭР
export const getWerCalculationCascadesPlants = (): Promise<IWerCalculationElementDto[]> =>
  api.get('/api/v1/wer-calculation/cascades-plants')

// Получение списка допустимых расчётных интервалов
export const getWerCalculationIntervals = (): Promise<IWerCalculationIntervalDto[]> =>
  api.get('/api/v1/wer-calculation/intervals')

// Получение списка станций, доступных для выбора в качестве боевых
export const getWerCalculationObjectivePlants = (
  plantsCascades: IWerCalculationElementDto[],
): Promise<IWerCalculationElementDto[]> => api.post('api/v1/wer-calculation/objective-plants', { plantsCascades })

// Создание нового расчёта
export const saveWerCalculation = (calculation: Partial<IWerCalculationDto>) =>
  api.post('/api/v1/wer-calculation', calculation)

// Редактирование расчёта
export const updateWerCalculation = (id: IWerCalculationDto['id'], calculation: Partial<IWerCalculationDto>) =>
  api.put(`/api/v1/wer-calculation/${id}`, calculation)

// Удаление расчёта
export const deleteWerCalculation = (id: IWerCalculationDto['id']) => api.delete(`/api/v1/wer-calculation/${id}`)

// Увеличение порядкового номера
export const increaseSerialWerCalculation = (id: IWerCalculationDto['id']) =>
  api.put(`/api/v1/wer-calculation/${id}/increase-serial`)

// Уменьшение порядкового номера
export const decreaseSerialWerCalculation = (id: IWerCalculationDto['id']) =>
  api.put(`/api/v1/wer-calculation/${id}/decrease-serial`)

// Уменьшение порядкового номера
export const getDepartments = (): Promise<IDepartmentDto[]> => api.get('/api/v1/registry/departments')
