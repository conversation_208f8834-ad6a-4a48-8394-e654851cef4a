import CloseIcon from '@mui/icons-material/Close'
import { Autocomplete, Box } from '@mui/material'
import Chip from '@mui/material/Chip/Chip'
import * as React from 'react'
import { classNames } from 'shared/lib/classNames'
import { ItemsProps } from 'shared/ui/Select/Select.tsx'
import { TextField } from 'shared/ui/TextField'

import cls from './AutocompletePlant.module.scss'

interface AutocompletePlantProps<T extends ItemsProps> {
  options: T[]
  value: T[]
  onChange: (event: React.SyntheticEvent, value: T[]) => void
  error: boolean
}

export const AutocompletePlant = <T extends ItemsProps>(props: AutocompletePlantProps<T>) => {
  const { options, value, onChange, error } = props

  return (
    <Autocomplete
      multiple
      options={options}
      className={cls.autocomplete}
      getOptionLabel={(option) => {
        if (typeof option === 'string') {
          return option
        }

        return option.label || ''
      }}
      value={value}
      onChange={onChange}
      renderTags={(selected, getTagProps) => (
        <Box className={cls.chipContainer}>
          {selected.map((value, idx) => (
            <Chip
              {...getTagProps({ index: idx })}
              className={classNames(
                cls.chipItem,
                {
                  [cls.chipError]: value.error,
                },
                [],
              )}
              label={value.label}
              deleteIcon={<CloseIcon className={cls.chipDeleteIcon} fontSize='small' />}
            />
          ))}
        </Box>
      )}
      slotProps={{
        popper: {
          sx: {
            '& .MuiAutocomplete-option': {
              fontSize: '14px',
            },
          },
        },
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          sx={{
            '& .MuiOutlinedInput-root': {
              padding: '3px 10px',
            },
            '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
              padding: 0,
              border: 0,
              borderRadius: 0,
            },
          }}
          error={error}
        />
      )}
    />
  )
}
