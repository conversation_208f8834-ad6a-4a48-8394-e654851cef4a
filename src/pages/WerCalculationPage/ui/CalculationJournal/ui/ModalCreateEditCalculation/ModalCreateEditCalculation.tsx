import { isAfter, isBefore } from 'date-fns'
import { observer } from 'mobx-react'
import { ErrorExplanationIcon } from 'pages/CalcModelWerPage/ui/ErrorExplanationIcon'
import { getFormDataDateValue } from 'pages/WerCalculationPage/ui/CalculationJournal/lib/getFormDataDateValue.ts'
import { useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Loader, Row } from 'shared/ui'
import { DatePicker } from 'shared/ui/DatePicker'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore.ts'

import { AutocompletePlant } from '../AutocompletePlant'
import cls from './ModalCreateEditCalculation.module.scss'

export const ModalCreateEditCalculation = observer(() => {
  const { calcModelWerStore } = useStore()
  const { calculationJournal } = calcModelWerStore
  const {
    selectedPlant,
    intervals,
    availableObjectivePlants,
    cascadesPlants,
    formData,
    formErrors,
    someObjectivePlantsHasError,
    isLoading,
    fetchIntervals,
    fetchObjectivePlants,
    fetchCascadesPlants,
    resetChanges,
    updateFormField,
    saveCalculation,
    getCascadesPlantsByIds,
    getObjectivePlantsByIds,
  } = calculationJournal
  const [loading, setLoading] = useState(true)
  const [overallStartDateWarning, setOverallStartDateWarning] = useState('')
  const [overallEndDateWarning, setOverallEndDateWarning] = useState('')

  useEffect(() => {
    Promise.all([fetchIntervals(), fetchCascadesPlants()]).then(() => {
      setLoading(false)
    })
  }, [])

  useEffect(() => {
    if (!loading) {
      if (intervals.length > 0 && !formData.calculationInterval) {
        updateFormField('calculationInterval', String(intervals[0].value))
      }
    }
  }, [loading])

  useEffect(() => {
    if (cascadesPlants.length > 0) {
      fetchObjectivePlants(formData.plantsCascades)
    }
  }, [formData.plantsCascades, cascadesPlants])

  useEffect(() => {
    if (selectedPlant && isAfter(new Date(formData.overallStartDate), new Date(selectedPlant?.overallStartDate))) {
      setOverallStartDateWarning('Данные до выбранной даты начала будут удалены безвозвратно')
    } else {
      setOverallStartDateWarning('')
    }
    if (selectedPlant && isBefore(new Date(formData.overallEndDate), new Date(selectedPlant.overallEndDate))) {
      setOverallEndDateWarning('Данные после выбранной даты окончания будут удалены безвозвратно')
    } else {
      setOverallEndDateWarning('')
    }
  }, [formData.overallStartDate, formData.overallEndDate])

  const handleSave = async () => {
    await saveCalculation()
  }

  return (
    <Modal
      open
      title={(selectedPlant === null ? 'Создание' : 'Редактирование') + ' расчёта'}
      maxWidth='lg'
      onClose={resetChanges}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            <LoadingButton
              variant='contained'
              onClick={handleSave}
              loading={isLoading}
              className={classNames(cls.saveButton, { [cls.saveButtonEdit]: selectedPlant !== null })}
            >
              {selectedPlant === null ? 'Создать' : 'Изменить'}
            </LoadingButton>
          </div>
        </div>
      }
    >
      <div className={classNames(cls.wrapper, { [cls.wrapperLoading]: loading }, [])}>
        {loading ? (
          <Loader />
        ) : (
          <>
            <Row label='Название' contentClassName={cls.rowContent}>
              <TextField
                type='string'
                className={cls.rowContent}
                value={formData.title}
                error={!!formErrors.title.length}
                onChange={(e) => updateFormField('title', e.target.value)}
              />
              <ErrorExplanationIcon title={formErrors.title.join('; ')} />
            </Row>
            <Row label='ГЭС/Каскад ГЭС' contentClassName={cls.rowContent}>
              <AutocompletePlant
                options={cascadesPlants}
                error={!!formErrors.plantsCascades.length}
                value={getCascadesPlantsByIds(formData.plantsCascades)}
                onChange={(_, values) => {
                  updateFormField(
                    'plantsCascades',
                    values.map((value) => value.id),
                  )
                }}
              />
              <ErrorExplanationIcon title={formErrors.plantsCascades.join('; ')} />
            </Row>
            <Row label='Дата начала расчета' contentClassName={cls.rowContent}>
              <DatePicker
                value={formData.startDate ? new Date(formData.startDate) : undefined}
                setValue={(date) => {
                  updateFormField('startDate', getFormDataDateValue(date))
                }}
                error={formErrors.startDate.join('; ')}
                showErrorMsg={false}
                handleAllDates
              />
              <ErrorExplanationIcon title={formErrors.startDate.join('; ')} />
            </Row>
            <Row label='Дата окончания расчета' contentClassName={cls.rowContent}>
              <DatePicker
                value={formData.endDate ? new Date(formData.endDate) : undefined}
                setValue={(date) => updateFormField('endDate', getFormDataDateValue(date))}
                error={formErrors.endDate.join('; ')}
                showErrorMsg={false}
                handleAllDates
              />
              <ErrorExplanationIcon title={formErrors.endDate.join('; ')} />
            </Row>
            <Row label='Общий расчетный период'>
              <Switch
                checked={formData.overallPeriod}
                onChange={(_, value) => updateFormField('overallPeriod', value)}
              />
            </Row>
            {formData.overallPeriod && (
              <>
                <Row label='Общая дата начала расчета' contentClassName={cls.rowContent}>
                  <DatePicker
                    value={formData.overallStartDate ? new Date(formData.overallStartDate) : undefined}
                    setValue={(date) => updateFormField('overallStartDate', getFormDataDateValue(date))}
                    error={formErrors.overallStartDate.join('; ')}
                    showErrorMsg={false}
                    handleAllDates
                  />
                  <ErrorExplanationIcon
                    title={
                      overallStartDateWarning
                        ? formErrors.overallStartDate.concat(overallStartDateWarning).join('; ')
                        : formErrors.overallStartDate.join('; ')
                    }
                  />
                </Row>
                <Row label='Общая дата окончания расчета' contentClassName={cls.rowContent}>
                  <DatePicker
                    value={formData.overallEndDate ? new Date(formData.overallEndDate) : undefined}
                    setValue={(date) => updateFormField('overallEndDate', getFormDataDateValue(date))}
                    error={formErrors.overallEndDate.join('; ')}
                    showErrorMsg={false}
                    handleAllDates
                  />
                  <ErrorExplanationIcon
                    title={
                      overallEndDateWarning
                        ? formErrors.overallEndDate.concat(overallEndDateWarning).join('; ')
                        : formErrors.overallEndDate.join('; ')
                    }
                  />
                </Row>
              </>
            )}
            <Row label='Описание' rowClassName={cls.row} contentClassName={cls.rowContent}>
              <TextField
                multiline
                maxRows={3}
                type='string'
                className={cls.rowContent}
                value={formData.description}
                onChange={(e) => updateFormField('description', e.target.value)}
              />
              <ErrorExplanationIcon title={formErrors.description.join('; ')} />
            </Row>
            <Row label='Боевой расчет'>
              <Switch checked={formData.objective} onChange={(_, value) => updateFormField('objective', value)} />
            </Row>
            {formData.objective && (
              <Row label='Боевые станции' contentClassName={cls.rowContent}>
                <AutocompletePlant
                  options={availableObjectivePlants}
                  error={!!formErrors.objectivePlants.length || someObjectivePlantsHasError}
                  value={getObjectivePlantsByIds(formData.objectivePlants)}
                  onChange={(_, values) => {
                    updateFormField(
                      'objectivePlants',
                      values.map((value) => value.id),
                    )
                  }}
                />
                <ErrorExplanationIcon title={formErrors.objectivePlants.join('; ')} />
              </Row>
            )}
            <Row label='Расч. интервал' contentClassName={cls.rowContent}>
              <Select
                variant='outlined'
                items={intervals}
                value={formData.calculationInterval}
                onChange={(value) => updateFormField('calculationInterval', String(value))}
              />
            </Row>
          </>
        )}
      </div>
    </Modal>
  )
})
