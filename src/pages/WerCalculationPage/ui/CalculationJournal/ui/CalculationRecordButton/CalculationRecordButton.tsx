import { Icon<PERSON>utton, Menu, MenuItem, Tooltip } from '@mui/material'
import { useTheme } from 'app/providers/ThemeProvider'
import { FC, MouseEventHandler, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Icon } from 'shared/ui'

import { IWerCalculationJournalStore } from '../../model'
import { CalculationDeleteMenuItem } from '../CalculationDeleteMenuItem'
import cls from './CalculationRecordButton.module.scss'

type CalculationRecord = IWerCalculationJournalStore['calculations'][0]

export interface CalculationRecordButtonProps {
  row: CalculationRecord
  canMoveRecord: boolean
  onTop: (tabId: CalculationRecord['tabId']) => Promise<void>
  onBottom: (tabId: CalculationRecord['tabId']) => Promise<void>
  onEdit: (tabId: CalculationRecord['tabId']) => void
  onDelete: (id: CalculationRecord['id']) => Promise<void>
}

export const CalculationRecordButton: FC<CalculationRecordButtonProps> = (props) => {
  const { row, canMoveRecord, onTop, onBottom, onEdit, onDelete } = props
  const { theme } = useTheme()
  const buttonRef = useRef<HTMLDivElement | null>(null)
  const [contextMenu, setContextMenu] = useState<{
    left: number
    top: number
  } | null>(null)

  const handleContextMenu: MouseEventHandler = (event) => {
    event.preventDefault()
    setContextMenu(
      contextMenu === null
        ? {
            left: event.clientX + 2,
            top: event.clientY - 6,
          }
        : null,
    )
  }

  const handleClose = () => {
    setContextMenu(null)
  }

  return (
    <>
      <div ref={buttonRef} className={cls.buttonWrapper}>
        <IconButton onClick={handleContextMenu} className={cls.editIcon}>
          <Icon name='points' width={13} height={13} />
        </IconButton>
      </div>
      <Menu
        open={contextMenu !== null}
        className={classNames(cls.menu, {}, [theme])}
        onClose={handleClose}
        anchorReference='anchorPosition'
        anchorPosition={contextMenu ?? undefined}
      >
        {canMoveRecord && (
          <>
            <Tooltip title='Уменьшить порядковый номер расчёта' placement='left'>
              <MenuItem onClick={() => onTop(row.tabId)} className={cls.menuItem}>
                <Icon name='arrowTop' height={16} width={16} className={cls.baseIcon} />
              </MenuItem>
            </Tooltip>
            <Tooltip title='Увеличить порядковый номер расчёта' placement='left'>
              <MenuItem onClick={() => onBottom(row.tabId)} className={cls.menuItem}>
                <Icon name='arrowBottom' height={16} width={16} className={cls.baseIcon} />
              </MenuItem>
            </Tooltip>
          </>
        )}
        <Tooltip title='Отредактировать расчёт' placement='left'>
          <MenuItem onClick={() => onEdit(row.tabId)} className={cls.menuItem}>
            <Icon name='settings' height={16} width={16} className={cls.baseIcon} />
          </MenuItem>
        </Tooltip>
        <CalculationDeleteMenuItem onDelete={onDelete} row={row} />
      </Menu>
    </>
  )
}
