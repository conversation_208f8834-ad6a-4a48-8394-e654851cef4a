.buttonWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;

  & > div:not(:last-child)::after {
    content: "";
    position: absolute;
    right: -5px;
    width: 2px;
    height: 100%;
    background-color: #ccc;
  }
}

.editIcon {
  padding: 0;
  color: var(--primary-color);
}

.baseIcon {
  color: var(--primary-color);
}

.menu {
  padding: 0;

  &Item:hover {
    & .baseIcon {
      color: var(--primary-color-invert);
    }
  }
}
