import { Icon<PERSON>utton, Tooltip } from '@mui/material'
import { format } from 'date-fns'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { IUserDetails } from 'entities/store/auth.entities.ts'
import { Icon } from 'shared/ui'
import { IColumn } from 'widgets/TableV1'
import { TFilterControlMultiSelectFiltering, TFilterControlRadioSelectFiltering } from 'widgets/TableV1/ui'

import { IWerCalculationDto } from '../api'
import cls from '../CalculationJournal.module.scss'
import { IWerCalculationJournalStore } from '../model'
import { CalculationRecordButton, CalculationRecordButtonProps } from '../ui/CalculationRecordButton'

type CalculationJournalColumns = (
  onAdd: () => void,
  onTop: CalculationRecordButtonProps['onTop'],
  onBottom: CalculationRecordButtonProps['onBottom'],
  onEdit: CalculationRecordButtonProps['onEdit'],
  onDelete: CalculationRecordButtonProps['onDelete'],
  objectiveFilterItems: TFilterControlRadioSelectFiltering['items'],
  departmentLevelsFilterItems: TFilterControlMultiSelectFiltering['items'],
  canMoveRecord: boolean,
  userDetails?: IUserDetails,
) => IColumn<IWerCalculationJournalStore['calculations'][0]>[]

export const calculationJournalColumns: CalculationJournalColumns = (
  onAdd,
  onTop,
  onBottom,
  onEdit,
  onDelete,
  objectiveFilterItems,
  departmentLevelsFilterItems,
  canMoveRecord,
  userDetails,
) => [
  {
    name: 'objective',
    title: '',
    width: 30,
    filtering: {
      type: 'radioselect',
      items: objectiveFilterItems,
    },
    render: (value: IWerCalculationDto['objective']) => {
      if (value) {
        return <Icon name='lightning' height='20px' className={cls.icon} />
      }
    },
  },
  {
    name: 'serialNumber',
    title: '№',
    width: 80,
    sortingEnabled: true,
    searchingEnabled: true,
  },
  {
    name: 'title',
    title: 'Название',
    width: 160,
    sortingEnabled: true,
    searchingEnabled: true,
  },
  {
    name: 'changedDatetime',
    title: 'Дата изменения',
    width: 200,
    sortingEnabled: true,
    searchingEnabled: true,
    editType: 'date',
    getCellRenderValue: (value: IWerCalculationDto['changedDatetime']) => format(new Date(value), 'dd.MM.yyy HH:mm:ss'),
  },
  {
    name: 'plantsCascades',
    title: 'ГЭС / каскад ГЭС',
    width: 230,
    sortingEnabled: true,
    searchingEnabled: true,
    getCellRenderValue: (value: IWerCalculationDto['plantsCascades']) => {
      const names = value.map((val) => val.name)

      return names.join(', ')
    },
  },
  {
    name: 'startDate',
    title: 'Дата начала расчета',
    width: 240,
    sortingEnabled: true,
    searchingEnabled: true,
    editType: 'date',
    getCellRenderValue: (value: IWerCalculationDto['startDate']) => format(new Date(value), 'dd.MM.yyy'),
  },
  {
    name: 'endDate',
    title: 'Дата окончания расчета',
    width: 240,
    sortingEnabled: true,
    searchingEnabled: true,
    editType: 'date',
    getCellRenderValue: (value: IWerCalculationDto['endDate']) => format(new Date(value), 'dd.MM.yyy'),
  },
  {
    name: 'description',
    title: 'Описание',
    width: 160,
    searchingEnabled: true,
  },
  {
    name: 'objectivePlants',
    title: 'Боевые станции',
    width: 180,
    searchingEnabled: true,
    editType: 'select',
    getCellRenderValue: (value: IWerCalculationDto['objectivePlants']) => {
      const names = value.map((val) => val.name)

      return names.join(', ')
    },
  },
  {
    name: 'department',
    title: 'ДЦ',
    width: 120,
    sortingEnabled: true,
    searchingEnabled: true,
    filtering: {
      type: 'treeselect',
      items: departmentLevelsFilterItems,
    },
    editType: 'select',
    getCellRenderValue: (value: IWerCalculationDto['department']) => value.name,
  },
  {
    name: 'action',
    title: '',
    width: 40,
    headRender: () => {
      return (
        <div className={cls.actionHeader}>
          <Tooltip title='Создать расчёт'>
            <IconButton
              sx={{
                color: 'var(--primary-color)',
                display: 'inline-flex!important',
              }}
              className={cls.icon}
              onClick={onAdd}
              disabled={!userDetails?.roles.some(({ role }) => role === ROLES.TECHNOLOGIST)}
            >
              <Icon name='plus' width={13} />
            </IconButton>
          </Tooltip>
        </div>
      )
    },
    render: (_, row) => {
      if (
        row.department.id === userDetails?.departmentId &&
        userDetails.roles.some(({ role }) => role === ROLES.TECHNOLOGIST)
      ) {
        return (
          <CalculationRecordButton
            row={row}
            canMoveRecord={canMoveRecord}
            onTop={onTop}
            onBottom={onBottom}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        )
      }
    },
  },
]
