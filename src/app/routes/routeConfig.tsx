import { ROLES } from 'entities/shared/roles.entities.ts'
import { AdministrationPage } from 'pages/AdministrationPage'
import { CalcModelPage } from 'pages/CalcModelPage'
import { CalcModelWerPage } from 'pages/CalcModelWerPage'
import { CalculationsPage } from 'pages/CalculationsPage'
import { GAESCalculationsPage } from 'pages/GAESCalculationsPage'
import { JournalsPage } from 'pages/JournalsPage'
import { MainPage } from 'pages/MainPage'
import { NotFoundPage } from 'pages/NotFoundPage'
import { NsiPage } from 'pages/NsiPage'
import { ReportsPage } from 'pages/ReportsPage'
import { SettingsPage } from 'pages/SettingsPage'
import { WerCalculationPage } from 'pages/WerCalculationPage'
import { type ReactNode } from 'react'
import { type RouteProps } from 'react-router-dom'

enum AppRoutes {
  MAIN = 'main',
  NOT_FOUND = 'not_found',
  ADMINISTRATION = 'administration',
  NSI = 'nsi',
  CALCMODEL = 'calc_model',
  CALCMODEL_WER = 'calc_model_wer',
  CALCULATIONS = 'calculations',
  SETTINGS = 'settings',
  REPORTS = 'reports',
  JOURNALS = 'journals',
  GAES_CALCULATIONS = 'gaes_calculations',
  CALCULATIONS_WER = 'calculations_wer',
}

export const RoutePath: Record<AppRoutes, string> = {
  [AppRoutes.MAIN]: '/',
  [AppRoutes.NOT_FOUND]: '*',
  [AppRoutes.ADMINISTRATION]: '/administration',
  [AppRoutes.NSI]: '/nsi',
  [AppRoutes.CALCMODEL]: '/calcModel',
  [AppRoutes.CALCMODEL_WER]: '/rm-wer',
  [AppRoutes.CALCULATIONS]: '/calculations',
  [AppRoutes.SETTINGS]: '/settings',
  [AppRoutes.REPORTS]: '/reports',
  [AppRoutes.JOURNALS]: '/journals',
  [AppRoutes.GAES_CALCULATIONS]: '/gaes-calculations',
  [AppRoutes.CALCULATIONS_WER]: '/calculation-wer',
}

interface IRouterProps extends Omit<RouteProps, 'access'> {
  access?: ROLES[]
  element?: ReactNode
  path?: string
}

export const routeConfig: Record<AppRoutes, IRouterProps> = {
  [AppRoutes.MAIN]: {
    path: RoutePath.main,
    element: <MainPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.NOT_FOUND]: {
    path: RoutePath.not_found,
    element: <NotFoundPage />,
    access: [],
  },
  [AppRoutes.NSI]: {
    path: RoutePath.nsi,
    element: <NsiPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.CALCMODEL]: {
    path: RoutePath.calc_model,
    element: <CalcModelPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.CALCMODEL_WER]: {
    path: RoutePath.calc_model_wer,
    element: <CalcModelWerPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.CALCULATIONS]: {
    path: RoutePath.calculations,
    element: <CalculationsPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.ADMINISTRATION]: {
    path: RoutePath.administration,
    element: <AdministrationPage />,
    access: [ROLES.ADMIN],
  },
  [AppRoutes.SETTINGS]: {
    path: RoutePath.settings,
    element: <SettingsPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM],
  },
  [AppRoutes.REPORTS]: {
    path: RoutePath.reports,
    element: <ReportsPage />,
    access: [ROLES.ADMIN],
  },
  [AppRoutes.JOURNALS]: {
    path: RoutePath.journals,
    element: <JournalsPage />,
    access: [ROLES.ADMIN],
  },
  [AppRoutes.GAES_CALCULATIONS]: {
    path: RoutePath.gaes_calculations,
    element: <GAESCalculationsPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST],
  },
  [AppRoutes.CALCULATIONS_WER]: {
    path: RoutePath.calculations_wer,
    element: <WerCalculationPage />,
    access: [ROLES.ADMIN, ROLES.TECH_ADMIN_CM, ROLES.TECH_ADMIN_NSI, ROLES.TECHNOLOGIST, ROLES.GUEST],
  },
}
