import { CollapsedProvider } from 'app/providers/CollapsedProvider'
import { PromptNoSaveProvider } from 'app/providers/PromptNoSaveProvider'
import { routeConfig } from 'app/routes/routeConfig'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { AuthorizationPage } from 'pages/AuthorizationPage'
import { type FC, ReactNode, Suspense, useEffect } from 'react'
import { createBrowserRouter, createRoutesFromElements, Navigate, Route, RouterProvider } from 'react-router-dom'
import { PageLoader } from 'shared/ui/PageLoader/PageLoader'
import { useStore } from 'stores/useStore'
import { Navbar } from 'widgets/Navbar'
import { SideBar } from 'widgets/SideBar'

interface IPrivateRoute {
  children: ReactNode
  access?: ROLES[]
}

export const AppRouter = observer(() => {
  const { authStore } = useStore()
  const { checkAuth } = authStore

  useEffect(() => {
    checkAuth()
  }, [])

  const PrivateRoute: FC<IPrivateRoute> = ({ children }) => {
    const auth = localStorage.getItem('token')

    // const isAccess = roles?.map((el: any) => el.role).some((role: any) => {
    //   return access.some((acc: any) => acc === role)
    // })
    return auth ? children : <Navigate to='/login' />
  }

  const router = createBrowserRouter(
    createRoutesFromElements(
      <>
        <Route
          path='/login'
          element={
            <Suspense
              fallback={
                <div className='content-page'>
                  <PageLoader />
                </div>
              }
            >
              <AuthorizationPage />
            </Suspense>
          }
        />
        {Object.values(routeConfig)
          .filter((route) => !(!isViewDevPage && route.path === '/calculation-wer'))
          .map(({ element, path, access = [] }) => {
            return (
              <Route
                key={path}
                element={
                  <PrivateRoute access={access}>
                    <PromptNoSaveProvider>
                      <CollapsedProvider>
                        <Navbar />
                        <div className='content-page'>
                          <SideBar />
                          <Suspense fallback={<PageLoader />}>
                            <div className='page-wrapper'>{element}</div>
                          </Suspense>
                        </div>
                      </CollapsedProvider>
                    </PromptNoSaveProvider>
                  </PrivateRoute>
                }
                path={path}
              />
            )
          })}
      </>,
    ),
  )

  return <RouterProvider router={router} />
})
